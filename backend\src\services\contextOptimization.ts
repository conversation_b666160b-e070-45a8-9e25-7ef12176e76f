/**
 * Serviço de Otimização de Contexto
 * Ajusta chunks para caber no limite de tokens com sumarização e compressão seletiva
 */

import { embeddingService } from './embeddingService';
import { geminiService } from './gemini';

export interface ContextOptimizationConfig {
  maxTokens: number;
  targetTokens: number;
  preserveImportant: boolean;
  compressionRatio: number;
  summarizationThreshold: number;
  useSemanticCompression: boolean;
  maintainCoherence: boolean;
}

export interface OptimizedContext {
  content: string;
  originalLength: number;
  optimizedLength: number;
  compressionRatio: number;
  tokensUsed: number;
  tokensRemaining: number;
  optimizationMethods: string[];
  qualityScore: number;
  chunks: OptimizedChunk[];
}

export interface OptimizedChunk {
  id: string;
  content: string;
  originalContent: string;
  importance: number;
  compressionRatio: number;
  method: 'original' | 'compressed' | 'summarized' | 'filtered';
  tokens: number;
  semanticScore?: number;
}

export interface CompressionStrategy {
  name: string;
  priority: number;
  enabled: boolean;
  apply: (text: string, config: any) => Promise<string>;
  estimateCompression: (text: string) => number;
}

export class ContextOptimizationService {
  private config: ContextOptimizationConfig;
  private compressionStrategies: CompressionStrategy[];

  constructor(config?: Partial<ContextOptimizationConfig>) {
    this.config = {
      maxTokens: 8000,
      targetTokens: 6000,
      preserveImportant: true,
      compressionRatio: 0.7,
      summarizationThreshold: 1000,
      useSemanticCompression: true,
      maintainCoherence: true,
      ...config
    };

    this.initializeCompressionStrategies();
  }

  /**
   * Otimizar contexto para caber no limite de tokens
   */
  async optimizeContext(
    chunks: Array<{ content: string; metadata?: any; importance?: number }>,
    query?: string
  ): Promise<OptimizedContext> {
    console.log(`🔧 Iniciando otimização de contexto: ${chunks.length} chunks`);

    const startTime = Date.now();
    const originalContent = chunks.map(c => c.content).join('\n\n');
    const originalLength = originalContent.length;
    const originalTokens = this.estimateTokens(originalContent);

    console.log(`📊 Conteúdo original: ${originalTokens} tokens, ${originalLength} caracteres`);

    // Se já está dentro do limite, retornar sem otimização
    if (originalTokens <= this.config.targetTokens) {
      return {
        content: originalContent,
        originalLength,
        optimizedLength: originalLength,
        compressionRatio: 1.0,
        tokensUsed: originalTokens,
        tokensRemaining: this.config.maxTokens - originalTokens,
        optimizationMethods: ['no_optimization_needed'],
        qualityScore: 1.0,
        chunks: chunks.map((chunk, index) => ({
          id: `chunk_${index}`,
          content: chunk.content,
          originalContent: chunk.content,
          importance: chunk.importance || 0.5,
          compressionRatio: 1.0,
          method: 'original' as const,
          tokens: this.estimateTokens(chunk.content)
        }))
      };
    }

    // Calcular importância dos chunks se não fornecida
    const chunksWithImportance = await this.calculateChunkImportance(chunks, query);

    // Aplicar estratégias de otimização
    const optimizedChunks = await this.applyOptimizationStrategies(chunksWithImportance);

    // Construir contexto final
    const optimizedContent = optimizedChunks
      .sort((a, b) => b.importance - a.importance)
      .map(chunk => chunk.content)
      .join('\n\n');

    const optimizedLength = optimizedContent.length;
    const optimizedTokens = this.estimateTokens(optimizedContent);
    const compressionRatio = optimizedLength / originalLength;

    // Calcular score de qualidade
    const qualityScore = await this.calculateQualityScore(
      originalContent,
      optimizedContent,
      compressionRatio
    );

    const processingTime = Date.now() - startTime;
    console.log(`✅ Otimização concluída: ${optimizedTokens} tokens (${(compressionRatio * 100).toFixed(1)}% do original) em ${processingTime}ms`);

    return {
      content: optimizedContent,
      originalLength,
      optimizedLength,
      compressionRatio,
      tokensUsed: optimizedTokens,
      tokensRemaining: this.config.maxTokens - optimizedTokens,
      optimizationMethods: this.getUsedMethods(optimizedChunks),
      qualityScore,
      chunks: optimizedChunks
    };
  }

  /**
   * Calcular importância dos chunks
   */
  private async calculateChunkImportance(
    chunks: Array<{ content: string; metadata?: any; importance?: number }>,
    query?: string
  ): Promise<Array<{ content: string; metadata?: any; importance: number }>> {
    const chunksWithImportance = [];

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      let importance = chunk.importance || 0.5;

      // Calcular importância baseada na query se fornecida
      if (query && this.config.useSemanticCompression) {
        try {
          const queryEmbedding = await embeddingService.generateEmbedding(query);
          const chunkEmbedding = await embeddingService.generateEmbedding(chunk.content);
          const similarity = this.cosineSimilarity(queryEmbedding.embedding, chunkEmbedding.embedding);
          importance = Math.max(importance, similarity);
        } catch (error) {
          console.warn('Erro ao calcular similaridade semântica:', error);
        }
      }

      // Fatores adicionais de importância
      importance += this.calculateContentImportance(chunk.content);
      importance += this.calculatePositionalImportance(i, chunks.length);

      // Normalizar entre 0 e 1
      importance = Math.min(1.0, Math.max(0.0, importance));

      chunksWithImportance.push({
        ...chunk,
        importance
      });
    }

    return chunksWithImportance;
  }

  /**
   * Aplicar estratégias de otimização
   */
  private async applyOptimizationStrategies(
    chunks: Array<{ content: string; metadata?: any; importance: number }>
  ): Promise<OptimizedChunk[]> {
    const optimizedChunks: OptimizedChunk[] = [];
    let currentTokens = 0;

    // Ordenar chunks por importância (mais importantes primeiro)
    const sortedChunks = chunks.sort((a, b) => b.importance - a.importance);

    for (let i = 0; i < sortedChunks.length; i++) {
      const chunk = sortedChunks[i];
      const chunkTokens = this.estimateTokens(chunk.content);
      
      // Verificar se ainda há espaço
      if (currentTokens + chunkTokens <= this.config.targetTokens) {
        // Adicionar chunk original
        optimizedChunks.push({
          id: `chunk_${i}`,
          content: chunk.content,
          originalContent: chunk.content,
          importance: chunk.importance,
          compressionRatio: 1.0,
          method: 'original',
          tokens: chunkTokens
        });
        currentTokens += chunkTokens;
      } else {
        // Tentar comprimir ou sumarizar
        const remainingTokens = this.config.targetTokens - currentTokens;
        
        if (remainingTokens > 50 && chunk.importance > 0.3) {
          const optimizedChunk = await this.optimizeChunk(chunk, remainingTokens);
          if (optimizedChunk && optimizedChunk.tokens <= remainingTokens) {
            optimizedChunks.push(optimizedChunk);
            currentTokens += optimizedChunk.tokens;
          }
        }
        
        // Se não há mais espaço, parar
        if (currentTokens >= this.config.targetTokens) {
          break;
        }
      }
    }

    return optimizedChunks;
  }

  /**
   * Otimizar chunk individual
   */
  private async optimizeChunk(
    chunk: { content: string; metadata?: any; importance: number },
    maxTokens: number
  ): Promise<OptimizedChunk | null> {
    const originalTokens = this.estimateTokens(chunk.content);
    
    // Tentar diferentes estratégias em ordem de prioridade
    for (const strategy of this.compressionStrategies.filter(s => s.enabled)) {
      try {
        const compressedContent = await strategy.apply(chunk.content, {
          maxTokens,
          targetRatio: maxTokens / originalTokens
        });
        
        const compressedTokens = this.estimateTokens(compressedContent);
        
        if (compressedTokens <= maxTokens && compressedContent.length > 0) {
          return {
            id: `optimized_${Date.now()}`,
            content: compressedContent,
            originalContent: chunk.content,
            importance: chunk.importance,
            compressionRatio: compressedContent.length / chunk.content.length,
            method: strategy.name as any,
            tokens: compressedTokens
          };
        }
      } catch (error) {
        console.warn(`Erro na estratégia ${strategy.name}:`, error);
        continue;
      }
    }

    return null;
  }

  /**
   * Inicializar estratégias de compressão
   */
  private initializeCompressionStrategies(): void {
    this.compressionStrategies = [
      {
        name: 'semantic_compression',
        priority: 1,
        enabled: this.config.useSemanticCompression,
        apply: this.semanticCompression.bind(this),
        estimateCompression: (text) => 0.7
      },
      {
        name: 'summarization',
        priority: 2,
        enabled: true,
        apply: this.summarizeText.bind(this),
        estimateCompression: (text) => 0.5
      },
      {
        name: 'sentence_filtering',
        priority: 3,
        enabled: true,
        apply: this.filterSentences.bind(this),
        estimateCompression: (text) => 0.8
      },
      {
        name: 'redundancy_removal',
        priority: 4,
        enabled: true,
        apply: this.removeRedundancy.bind(this),
        estimateCompression: (text) => 0.9
      }
    ];

    // Ordenar por prioridade
    this.compressionStrategies.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Compressão semântica usando embeddings
   */
  private async semanticCompression(text: string, config: any): Promise<string> {
    const sentences = this.splitIntoSentences(text);
    if (sentences.length <= 2) return text;

    const targetCount = Math.ceil(sentences.length * (config.targetRatio || 0.7));
    
    // Calcular importância semântica de cada sentença
    const sentenceScores = await Promise.all(
      sentences.map(async (sentence, index) => {
        let score = 0;
        
        // Score baseado na posição (primeira e última sentenças são importantes)
        if (index === 0 || index === sentences.length - 1) {
          score += 0.3;
        }
        
        // Score baseado no comprimento (sentenças muito curtas ou muito longas são menos importantes)
        const length = sentence.split(' ').length;
        if (length >= 5 && length <= 25) {
          score += 0.2;
        }
        
        // Score baseado em palavras-chave importantes
        const importantWords = ['importante', 'principal', 'fundamental', 'essencial', 'crítico'];
        for (const word of importantWords) {
          if (sentence.toLowerCase().includes(word)) {
            score += 0.1;
          }
        }
        
        return { sentence, score, index };
      })
    );

    // Selecionar sentenças mais importantes
    const selectedSentences = sentenceScores
      .sort((a, b) => b.score - a.score)
      .slice(0, targetCount)
      .sort((a, b) => a.index - b.index)
      .map(item => item.sentence);

    return selectedSentences.join(' ');
  }

  /**
   * Sumarização usando Gemini
   */
  private async summarizeText(text: string, config: any): Promise<string> {
    if (text.length < this.config.summarizationThreshold) {
      return text;
    }

    try {
      const maxWords = Math.ceil(text.split(' ').length * (config.targetRatio || 0.5));
      
      const prompt = `Resuma o seguinte texto em no máximo ${maxWords} palavras, mantendo as informações mais importantes:

${text}

Resumo:`;

      const summary = await geminiService.generateContent(prompt);
      return summary.trim();
    } catch (error) {
      console.warn('Erro na sumarização:', error);
      return this.fallbackSummarization(text, config);
    }
  }

  /**
   * Filtrar sentenças menos importantes
   */
  private async filterSentences(text: string, config: any): Promise<string> {
    const sentences = this.splitIntoSentences(text);
    const targetCount = Math.ceil(sentences.length * (config.targetRatio || 0.8));
    
    if (sentences.length <= targetCount) return text;

    // Filtrar sentenças muito curtas ou com pouco conteúdo
    const filteredSentences = sentences.filter(sentence => {
      const words = sentence.split(' ').filter(w => w.length > 2);
      return words.length >= 3; // Manter sentenças com pelo menos 3 palavras significativas
    });

    return filteredSentences.slice(0, targetCount).join(' ');
  }

  /**
   * Remover redundância
   */
  private async removeRedundancy(text: string, config: any): Promise<string> {
    const sentences = this.splitIntoSentences(text);
    const uniqueSentences: string[] = [];
    const seenContent = new Set<string>();

    for (const sentence of sentences) {
      const normalized = sentence.toLowerCase().replace(/[^\w\s]/g, '').trim();
      
      // Verificar se é muito similar a alguma sentença já vista
      let isDuplicate = false;
      for (const seen of seenContent) {
        if (this.calculateSimilarity(normalized, seen) > 0.8) {
          isDuplicate = true;
          break;
        }
      }

      if (!isDuplicate) {
        uniqueSentences.push(sentence);
        seenContent.add(normalized);
      }
    }

    return uniqueSentences.join(' ');
  }

  /**
   * Métodos auxiliares
   */
  private estimateTokens(text: string): number {
    // Estimativa aproximada: 1 token ≈ 4 caracteres para português
    return Math.ceil(text.length / 4);
  }

  private splitIntoSentences(text: string): string[] {
    return text
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }

  private calculateContentImportance(content: string): number {
    let importance = 0;

    // Palavras-chave importantes
    const importantKeywords = [
      'importante', 'fundamental', 'essencial', 'crítico', 'principal',
      'objetivo', 'meta', 'resultado', 'conclusão', 'resumo'
    ];

    const lowerContent = content.toLowerCase();
    for (const keyword of importantKeywords) {
      if (lowerContent.includes(keyword)) {
        importance += 0.1;
      }
    }

    // Presença de números e dados
    if (/\d+/.test(content)) {
      importance += 0.05;
    }

    // Presença de listas ou estruturas
    if (/[-*•]\s/.test(content) || /\d+\.\s/.test(content)) {
      importance += 0.05;
    }

    return Math.min(0.3, importance);
  }

  private calculatePositionalImportance(index: number, total: number): number {
    // Primeira e última posições são mais importantes
    if (index === 0 || index === total - 1) {
      return 0.1;
    }

    // Posições no meio têm importância decrescente
    const middleDistance = Math.abs(index - total / 2) / (total / 2);
    return 0.05 * (1 - middleDistance);
  }

  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }

  private calculateSimilarity(text1: string, text2: string): number {
    const words1 = new Set(text1.split(' '));
    const words2 = new Set(text2.split(' '));
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  private async calculateQualityScore(
    original: string,
    optimized: string,
    compressionRatio: number
  ): Promise<number> {
    let qualityScore = 0.5; // Score base

    // Penalizar compressão excessiva
    if (compressionRatio < 0.3) {
      qualityScore -= 0.2;
    } else if (compressionRatio > 0.8) {
      qualityScore += 0.1;
    }

    // Verificar se mantém informações importantes
    const importantWords = ['importante', 'principal', 'fundamental', 'objetivo'];
    let preservedImportant = 0;
    let totalImportant = 0;

    for (const word of importantWords) {
      if (original.toLowerCase().includes(word)) {
        totalImportant++;
        if (optimized.toLowerCase().includes(word)) {
          preservedImportant++;
        }
      }
    }

    if (totalImportant > 0) {
      qualityScore += 0.3 * (preservedImportant / totalImportant);
    }

    // Verificar coerência (implementação básica)
    const originalSentences = this.splitIntoSentences(original).length;
    const optimizedSentences = this.splitIntoSentences(optimized).length;

    if (optimizedSentences > 0 && originalSentences > 0) {
      const sentenceRatio = optimizedSentences / originalSentences;
      if (sentenceRatio > 0.3 && sentenceRatio < 0.9) {
        qualityScore += 0.2;
      }
    }

    return Math.min(1.0, Math.max(0.0, qualityScore));
  }

  private getUsedMethods(chunks: OptimizedChunk[]): string[] {
    const methods = new Set<string>();
    for (const chunk of chunks) {
      methods.add(chunk.method);
    }
    return Array.from(methods);
  }

  private fallbackSummarization(text: string, config: any): string {
    // Sumarização simples baseada em sentenças importantes
    const sentences = this.splitIntoSentences(text);
    const targetCount = Math.ceil(sentences.length * (config.targetRatio || 0.5));

    // Selecionar primeira, última e algumas sentenças do meio
    const selected: string[] = [];

    if (sentences.length > 0) {
      selected.push(sentences[0]); // Primeira sentença
    }

    if (sentences.length > 2) {
      selected.push(sentences[sentences.length - 1]); // Última sentença
    }

    // Adicionar sentenças do meio se necessário
    const remaining = targetCount - selected.length;
    if (remaining > 0 && sentences.length > 2) {
      const middleStart = Math.floor(sentences.length * 0.3);
      const middleEnd = Math.floor(sentences.length * 0.7);
      const middleSentences = sentences.slice(middleStart, middleEnd);

      selected.push(...middleSentences.slice(0, remaining));
    }

    return selected.join(' ');
  }

  /**
   * Configurar limites de tokens
   */
  setTokenLimits(maxTokens: number, targetTokens?: number): void {
    this.config.maxTokens = maxTokens;
    this.config.targetTokens = targetTokens || Math.floor(maxTokens * 0.8);

    console.log(`🔧 Limites de tokens atualizados: máximo ${maxTokens}, alvo ${this.config.targetTokens}`);
  }

  /**
   * Obter configuração atual
   */
  getConfig(): ContextOptimizationConfig {
    return { ...this.config };
  }

  /**
   * Atualizar configuração
   */
  updateConfig(newConfig: Partial<ContextOptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.initializeCompressionStrategies(); // Reinicializar estratégias

    console.log('🔧 Configuração de otimização de contexto atualizada');
  }
}

// Instância singleton
export const contextOptimizationService = new ContextOptimizationService();
