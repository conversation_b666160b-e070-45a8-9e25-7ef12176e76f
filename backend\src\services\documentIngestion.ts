/**
 * Serviço de Ingestão e Pré-processamento de Documentos
 * Captura documentos, planilhas, APIs e multimídia com extração, limpeza e normalização
 */

import * as fs from 'fs';
import * as path from 'path';
import * as xlsx from 'xlsx';
import pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import FormData from 'form-data';

export interface IngestionSource {
  type: 'file' | 'url' | 'api' | 'database' | 'multimedia';
  source: string;
  metadata?: Record<string, any>;
  credentials?: Record<string, any>;
}

export interface ExtractedContent {
  id: string;
  content: string;
  metadata: {
    title: string;
    sourceType: string;
    originalSource: string;
    extractedAt: string;
    contentType: string;
    language?: string;
    wordCount: number;
    characterCount: number;
    encoding?: string;
    fileSize?: number;
    pages?: number;
    [key: string]: any;
  };
  chunks?: ContentChunk[];
  rawData?: any;
}

export interface ContentChunk {
  id: string;
  content: string;
  index: number;
  startPosition: number;
  endPosition: number;
  metadata: Record<string, any>;
}

export interface IngestionResult {
  success: boolean;
  contentId?: string;
  extractedContent?: ExtractedContent;
  error?: string;
  processingTime: number;
  warnings?: string[];
}

export interface IngestionConfig {
  maxFileSize: number; // bytes
  supportedFormats: string[];
  textExtraction: {
    preserveFormatting: boolean;
    extractImages: boolean;
    extractTables: boolean;
    ocrEnabled: boolean;
  };
  preprocessing: {
    normalizeWhitespace: boolean;
    removeEmptyLines: boolean;
    detectLanguage: boolean;
    cleanHtml: boolean;
    extractMetadata: boolean;
  };
  chunking: {
    enabled: boolean;
    strategy: 'fixed' | 'semantic' | 'hierarchical' | 'sliding';
    chunkSize: number;
    overlap: number;
  };
}

export class DocumentIngestionService {
  private config: IngestionConfig;
  private supportedMimeTypes: Map<string, string[]>;

  constructor(config?: Partial<IngestionConfig>) {
    this.config = {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      supportedFormats: ['pdf', 'docx', 'doc', 'txt', 'xlsx', 'xls', 'csv', 'json', 'xml', 'html'],
      textExtraction: {
        preserveFormatting: true,
        extractImages: false,
        extractTables: true,
        ocrEnabled: false
      },
      preprocessing: {
        normalizeWhitespace: true,
        removeEmptyLines: true,
        detectLanguage: true,
        cleanHtml: true,
        extractMetadata: true
      },
      chunking: {
        enabled: true,
        strategy: 'semantic',
        chunkSize: 1000,
        overlap: 200
      },
      ...config
    };

    this.initializeMimeTypes();
  }

  private initializeMimeTypes(): void {
    this.supportedMimeTypes = new Map([
      ['application/pdf', ['pdf']],
      ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', ['docx']],
      ['application/msword', ['doc']],
      ['text/plain', ['txt']],
      ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', ['xlsx']],
      ['application/vnd.ms-excel', ['xls']],
      ['text/csv', ['csv']],
      ['application/json', ['json']],
      ['application/xml', ['xml']],
      ['text/xml', ['xml']],
      ['text/html', ['html']],
      ['image/jpeg', ['jpg', 'jpeg']],
      ['image/png', ['png']],
      ['audio/mpeg', ['mp3']],
      ['audio/wav', ['wav']],
      ['video/mp4', ['mp4']]
    ]);
  }

  /**
   * Ingerir conteúdo de múltiplas fontes
   */
  async ingestMultipleSources(sources: IngestionSource[]): Promise<IngestionResult[]> {
    console.log(`📥 Iniciando ingestão de ${sources.length} fontes...`);
    
    const results: IngestionResult[] = [];
    
    for (let i = 0; i < sources.length; i++) {
      const source = sources[i];
      console.log(`📄 Processando fonte ${i + 1}/${sources.length}: ${source.type} - ${source.source}`);
      
      try {
        const result = await this.ingestSingleSource(source);
        results.push(result);
        
        // Delay entre processamentos para evitar sobrecarga
        if (i < sources.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error: any) {
        results.push({
          success: false,
          error: error.message,
          processingTime: 0
        });
      }
    }
    
    const successful = results.filter(r => r.success).length;
    console.log(`✅ Ingestão concluída: ${successful}/${sources.length} fontes processadas com sucesso`);
    
    return results;
  }

  /**
   * Ingerir conteúdo de uma única fonte
   */
  async ingestSingleSource(source: IngestionSource): Promise<IngestionResult> {
    const startTime = Date.now();
    
    try {
      let extractedContent: ExtractedContent;
      
      switch (source.type) {
        case 'file':
          extractedContent = await this.ingestFile(source.source, source.metadata);
          break;
        case 'url':
          extractedContent = await this.ingestUrl(source.source, source.metadata);
          break;
        case 'api':
          extractedContent = await this.ingestApi(source.source, source.credentials, source.metadata);
          break;
        case 'database':
          extractedContent = await this.ingestDatabase(source.source, source.credentials, source.metadata);
          break;
        case 'multimedia':
          extractedContent = await this.ingestMultimedia(source.source, source.metadata);
          break;
        default:
          throw new Error(`Tipo de fonte não suportado: ${source.type}`);
      }
      
      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        contentId: extractedContent.id,
        extractedContent,
        processingTime
      };
      
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      
      return {
        success: false,
        error: error.message,
        processingTime
      };
    }
  }

  /**
   * Ingerir arquivo local
   */
  private async ingestFile(filePath: string, metadata?: Record<string, any>): Promise<ExtractedContent> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`Arquivo não encontrado: ${filePath}`);
    }

    const stats = fs.statSync(filePath);
    if (stats.size > this.config.maxFileSize) {
      throw new Error(`Arquivo muito grande: ${stats.size} bytes (máximo: ${this.config.maxFileSize})`);
    }

    const ext = path.extname(filePath).toLowerCase().substring(1);
    if (!this.config.supportedFormats.includes(ext)) {
      throw new Error(`Formato não suportado: ${ext}`);
    }

    let content: string;
    let extractedMetadata: Record<string, any> = {};

    switch (ext) {
      case 'pdf':
        const pdfBuffer = fs.readFileSync(filePath);
        const pdfData = await pdfParse(pdfBuffer);
        content = pdfData.text;
        extractedMetadata.pages = pdfData.numpages;
        break;

      case 'docx':
        const docxBuffer = fs.readFileSync(filePath);
        const docxResult = await mammoth.extractRawText({ buffer: docxBuffer });
        content = docxResult.value;
        break;

      case 'txt':
        content = fs.readFileSync(filePath, 'utf-8');
        break;

      case 'xlsx':
      case 'xls':
        const workbook = xlsx.readFile(filePath);
        const sheets = workbook.SheetNames;
        content = sheets.map(sheetName => {
          const worksheet = workbook.Sheets[sheetName];
          return xlsx.utils.sheet_to_csv(worksheet);
        }).join('\n\n');
        extractedMetadata.sheets = sheets;
        break;

      case 'csv':
        content = fs.readFileSync(filePath, 'utf-8');
        break;

      case 'json':
        const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
        content = JSON.stringify(jsonData, null, 2);
        extractedMetadata.jsonStructure = typeof jsonData;
        break;

      default:
        content = fs.readFileSync(filePath, 'utf-8');
    }

    // Pré-processamento
    content = this.preprocessText(content);

    const extractedContent: ExtractedContent = {
      id: uuidv4(),
      content,
      metadata: {
        title: path.basename(filePath),
        sourceType: 'file',
        originalSource: filePath,
        extractedAt: new Date().toISOString(),
        contentType: ext,
        wordCount: content.split(/\s+/).filter(w => w.length > 0).length,
        characterCount: content.length,
        fileSize: stats.size,
        ...extractedMetadata,
        ...metadata
      }
    };

    // Chunking se habilitado
    if (this.config.chunking.enabled) {
      extractedContent.chunks = await this.createChunks(content, extractedContent.metadata);
    }

    return extractedContent;
  }
