/**
 * Serviço de Ingestão e Pré-processamento de Documentos
 * Captura documentos, planilhas, APIs e multimídia com extração, limpeza e normalização
 */

import * as fs from 'fs';
import * as path from 'path';
import * as xlsx from 'xlsx';
import pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import FormData from 'form-data';

export interface IngestionSource {
  type: 'file' | 'url' | 'api' | 'database' | 'multimedia';
  source: string;
  metadata?: Record<string, any>;
  credentials?: Record<string, any>;
}

export interface ExtractedContent {
  id: string;
  content: string;
  metadata: {
    title: string;
    sourceType: string;
    originalSource: string;
    extractedAt: string;
    contentType: string;
    language?: string;
    wordCount: number;
    characterCount: number;
    encoding?: string;
    fileSize?: number;
    pages?: number;
    [key: string]: any;
  };
  chunks?: ContentChunk[];
  rawData?: any;
}

export interface ContentChunk {
  id: string;
  content: string;
  index: number;
  startPosition: number;
  endPosition: number;
  metadata: Record<string, any>;
}

export interface IngestionResult {
  success: boolean;
  contentId?: string;
  extractedContent?: ExtractedContent;
  error?: string;
  processingTime: number;
  warnings?: string[];
}

export interface IngestionConfig {
  maxFileSize: number; // bytes
  supportedFormats: string[];
  textExtraction: {
    preserveFormatting: boolean;
    extractImages: boolean;
    extractTables: boolean;
    ocrEnabled: boolean;
  };
  preprocessing: {
    normalizeWhitespace: boolean;
    removeEmptyLines: boolean;
    detectLanguage: boolean;
    cleanHtml: boolean;
    extractMetadata: boolean;
  };
  chunking: {
    enabled: boolean;
    strategy: 'fixed' | 'semantic' | 'hierarchical' | 'sliding';
    chunkSize: number;
    overlap: number;
  };
}

export class DocumentIngestionService {
  private config: IngestionConfig;
  private supportedMimeTypes: Map<string, string[]> = new Map([
    ['text', ['text/plain', 'text/markdown', 'text/csv']],
    ['pdf', ['application/pdf']],
    ['doc', ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']],
    ['json', ['application/json']],
    ['xml', ['application/xml', 'text/xml']]
  ]);

  constructor(config?: Partial<IngestionConfig>) {
    this.config = {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      supportedFormats: ['pdf', 'docx', 'doc', 'txt', 'xlsx', 'xls', 'csv', 'json', 'xml', 'html'],
      textExtraction: {
        preserveFormatting: true,
        extractImages: false,
        extractTables: true,
        ocrEnabled: false
      },
      preprocessing: {
        normalizeWhitespace: true,
        removeEmptyLines: true,
        detectLanguage: true,
        cleanHtml: true,
        extractMetadata: true
      },
      chunking: {
        enabled: true,
        strategy: 'semantic',
        chunkSize: 1000,
        overlap: 200
      },
      ...config
    };

    this.initializeMimeTypes();
  }

  private initializeMimeTypes(): void {
    this.supportedMimeTypes = new Map([
      ['application/pdf', ['pdf']],
      ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', ['docx']],
      ['application/msword', ['doc']],
      ['text/plain', ['txt']],
      ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', ['xlsx']],
      ['application/vnd.ms-excel', ['xls']],
      ['text/csv', ['csv']],
      ['application/json', ['json']],
      ['application/xml', ['xml']],
      ['text/xml', ['xml']],
      ['text/html', ['html']],
      ['image/jpeg', ['jpg', 'jpeg']],
      ['image/png', ['png']],
      ['audio/mpeg', ['mp3']],
      ['audio/wav', ['wav']],
      ['video/mp4', ['mp4']]
    ]);
  }

  /**
   * Ingerir conteúdo de múltiplas fontes
   */
  async ingestMultipleSources(sources: IngestionSource[]): Promise<IngestionResult[]> {
    console.log(`📥 Iniciando ingestão de ${sources.length} fontes...`);
    
    const results: IngestionResult[] = [];
    
    for (let i = 0; i < sources.length; i++) {
      const source = sources[i];
      console.log(`📄 Processando fonte ${i + 1}/${sources.length}: ${source.type} - ${source.source}`);
      
      try {
        const result = await this.ingestSingleSource(source);
        results.push(result);
        
        // Delay entre processamentos para evitar sobrecarga
        if (i < sources.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error: any) {
        results.push({
          success: false,
          error: error.message,
          processingTime: 0
        });
      }
    }
    
    const successful = results.filter(r => r.success).length;
    console.log(`✅ Ingestão concluída: ${successful}/${sources.length} fontes processadas com sucesso`);
    
    return results;
  }

  /**
   * Ingerir conteúdo de uma única fonte
   */
  async ingestSingleSource(source: IngestionSource): Promise<IngestionResult> {
    const startTime = Date.now();
    
    try {
      let extractedContent: ExtractedContent;
      
      switch (source.type) {
        case 'file':
          extractedContent = await this.ingestFile(source.source, source.metadata);
          break;
        case 'url':
          extractedContent = await this.ingestUrl(source.source, source.metadata);
          break;
        case 'api':
          extractedContent = await this.ingestApi(source.source, source.credentials, source.metadata);
          break;
        case 'database':
          extractedContent = await this.ingestDatabase(source.source, source.credentials, source.metadata);
          break;
        case 'multimedia':
          extractedContent = await this.ingestMultimedia(source.source, source.metadata);
          break;
        default:
          throw new Error(`Tipo de fonte não suportado: ${source.type}`);
      }
      
      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        contentId: extractedContent.id,
        extractedContent,
        processingTime
      };
      
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      
      return {
        success: false,
        error: error.message,
        processingTime
      };
    }
  }

  /**
   * Ingerir arquivo local
   */
  private async ingestFile(filePath: string, metadata?: Record<string, any>): Promise<ExtractedContent> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`Arquivo não encontrado: ${filePath}`);
    }

    const stats = fs.statSync(filePath);
    if (stats.size > this.config.maxFileSize) {
      throw new Error(`Arquivo muito grande: ${stats.size} bytes (máximo: ${this.config.maxFileSize})`);
    }

    const ext = path.extname(filePath).toLowerCase().substring(1);
    if (!this.config.supportedFormats.includes(ext)) {
      throw new Error(`Formato não suportado: ${ext}`);
    }

    let content: string;
    let extractedMetadata: Record<string, any> = {};

    switch (ext) {
      case 'pdf':
        const pdfBuffer = fs.readFileSync(filePath);
        const pdfData = await pdfParse(pdfBuffer);
        content = pdfData.text;
        extractedMetadata.pages = pdfData.numpages;
        break;

      case 'docx':
        const docxBuffer = fs.readFileSync(filePath);
        const docxResult = await mammoth.extractRawText({ buffer: docxBuffer });
        content = docxResult.value;
        break;

      case 'txt':
        content = fs.readFileSync(filePath, 'utf-8');
        break;

      case 'xlsx':
      case 'xls':
        const workbook = xlsx.readFile(filePath);
        const sheets = workbook.SheetNames;
        content = sheets.map(sheetName => {
          const worksheet = workbook.Sheets[sheetName];
          return xlsx.utils.sheet_to_csv(worksheet);
        }).join('\n\n');
        extractedMetadata.sheets = sheets;
        break;

      case 'csv':
        content = fs.readFileSync(filePath, 'utf-8');
        break;

      case 'json':
        const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
        content = JSON.stringify(jsonData, null, 2);
        extractedMetadata.jsonStructure = typeof jsonData;
        break;

      default:
        content = fs.readFileSync(filePath, 'utf-8');
    }

    // Pré-processamento
    content = this.preprocessText(content);

    const extractedContent: ExtractedContent = {
      id: uuidv4(),
      content,
      metadata: {
        title: path.basename(filePath),
        sourceType: 'file',
        originalSource: filePath,
        extractedAt: new Date().toISOString(),
        contentType: ext,
        wordCount: content.split(/\s+/).filter(w => w.length > 0).length,
        characterCount: content.length,
        fileSize: stats.size,
        ...extractedMetadata,
        ...metadata
      }
    };

    // Chunking se habilitado
    if (this.config.chunking.enabled) {
      extractedContent.chunks = await this.createChunks(content, extractedContent.metadata);
    }

    return extractedContent;
  }

  /**
   * Ingerir conteúdo de URL
   */
  private async ingestUrl(url: string, metadata?: Record<string, any>): Promise<ExtractedContent> {
    try {
      const response = await axios.get(url, {
        timeout: 30000,
        maxContentLength: this.config.maxFileSize,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; RAG-Bot/1.0)'
        }
      });

      let content: string;
      const contentType = response.headers['content-type'] || '';

      if (contentType.includes('application/json')) {
        content = JSON.stringify(response.data, null, 2);
      } else if (contentType.includes('text/html')) {
        content = this.extractTextFromHtml(response.data);
      } else {
        content = response.data.toString();
      }

      content = this.preprocessText(content);

      const extractedContent: ExtractedContent = {
        id: uuidv4(),
        content,
        metadata: {
          title: this.extractTitleFromUrl(url),
          sourceType: 'url',
          originalSource: url,
          extractedAt: new Date().toISOString(),
          contentType: contentType,
          wordCount: content.split(/\s+/).filter(w => w.length > 0).length,
          characterCount: content.length,
          httpStatus: response.status,
          ...metadata
        }
      };

      if (this.config.chunking.enabled) {
        extractedContent.chunks = await this.createChunks(content, extractedContent.metadata);
      }

      return extractedContent;
    } catch (error: any) {
      throw new Error(`Erro ao ingerir URL ${url}: ${error.message}`);
    }
  }

  /**
   * Ingerir dados de API
   */
  private async ingestApi(endpoint: string, credentials?: Record<string, any>, metadata?: Record<string, any>): Promise<ExtractedContent> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (credentials?.apiKey) {
        headers['Authorization'] = `Bearer ${credentials.apiKey}`;
      }
      if (credentials?.headers) {
        Object.assign(headers, credentials.headers);
      }

      const response = await axios.get(endpoint, {
        headers,
        timeout: 30000,
        maxContentLength: this.config.maxFileSize
      });

      const content = JSON.stringify(response.data, null, 2);
      const processedContent = this.preprocessText(content);

      const extractedContent: ExtractedContent = {
        id: uuidv4(),
        content: processedContent,
        metadata: {
          title: `API Data from ${endpoint}`,
          sourceType: 'api',
          originalSource: endpoint,
          extractedAt: new Date().toISOString(),
          contentType: 'application/json',
          wordCount: processedContent.split(/\s+/).filter(w => w.length > 0).length,
          characterCount: processedContent.length,
          apiEndpoint: endpoint,
          httpStatus: response.status,
          ...metadata
        },
        rawData: response.data
      };

      if (this.config.chunking.enabled) {
        extractedContent.chunks = await this.createChunks(processedContent, extractedContent.metadata);
      }

      return extractedContent;
    } catch (error: any) {
      throw new Error(`Erro ao ingerir API ${endpoint}: ${error.message}`);
    }
  }

  /**
   * Ingerir dados de banco de dados
   */
  private async ingestDatabase(connectionString: string, credentials?: Record<string, any>, metadata?: Record<string, any>): Promise<ExtractedContent> {
    // Implementação simplificada - pode ser expandida para diferentes tipos de DB
    throw new Error('Ingestão de banco de dados não implementada ainda');
  }

  /**
   * Ingerir conteúdo multimídia
   */
  private async ingestMultimedia(filePath: string, metadata?: Record<string, any>): Promise<ExtractedContent> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`Arquivo multimídia não encontrado: ${filePath}`);
    }

    const ext = path.extname(filePath).toLowerCase().substring(1);
    const stats = fs.statSync(filePath);

    // Para arquivos de áudio/vídeo, extrair metadados básicos
    let content = `Arquivo multimídia: ${path.basename(filePath)}`;
    let extractedMetadata: Record<string, any> = {
      mediaType: ext,
      fileSize: stats.size,
      duration: null // Poderia usar ffprobe para extrair duração
    };

    // Para imagens, poderia usar OCR se habilitado
    if (['jpg', 'jpeg', 'png'].includes(ext) && this.config.textExtraction.ocrEnabled) {
      // Implementar OCR aqui se necessário
      content += '\n[OCR não implementado ainda]';
    }

    const extractedContent: ExtractedContent = {
      id: uuidv4(),
      content,
      metadata: {
        title: path.basename(filePath),
        sourceType: 'multimedia',
        originalSource: filePath,
        extractedAt: new Date().toISOString(),
        contentType: ext,
        wordCount: content.split(/\s+/).filter(w => w.length > 0).length,
        characterCount: content.length,
        ...extractedMetadata,
        ...metadata
      }
    };

    return extractedContent;
  }

  /**
   * Pré-processar texto
   */
  private preprocessText(text: string): string {
    let processed = text;

    if (this.config.preprocessing.normalizeWhitespace) {
      // Normalizar espaços em branco
      processed = processed.replace(/\s+/g, ' ');
    }

    if (this.config.preprocessing.removeEmptyLines) {
      // Remover linhas vazias
      processed = processed.replace(/^\s*[\r\n]/gm, '');
    }

    if (this.config.preprocessing.cleanHtml) {
      // Remover tags HTML básicas
      processed = processed.replace(/<[^>]*>/g, '');
      processed = processed.replace(/&[a-zA-Z0-9#]+;/g, ' ');
    }

    return processed.trim();
  }

  /**
   * Extrair texto de HTML
   */
  private extractTextFromHtml(html: string): string {
    // Implementação básica - poderia usar cheerio para melhor parsing
    let text = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
    text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    text = text.replace(/<[^>]*>/g, ' ');
    text = text.replace(/&[a-zA-Z0-9#]+;/g, ' ');
    return text.replace(/\s+/g, ' ').trim();
  }

  /**
   * Extrair título de URL
   */
  private extractTitleFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/').filter(p => p.length > 0);
      return pathParts.length > 0 ? pathParts[pathParts.length - 1] : urlObj.hostname;
    } catch {
      return url;
    }
  }

  /**
   * Criar chunks do conteúdo
   */
  private async createChunks(content: string, metadata: Record<string, any>): Promise<ContentChunk[]> {
    const chunks: ContentChunk[] = [];
    const { chunkSize, overlap, strategy } = this.config.chunking;

    switch (strategy) {
      case 'fixed':
        return this.createFixedChunks(content, chunkSize, overlap);
      case 'semantic':
        return this.createSemanticChunks(content, chunkSize, overlap);
      case 'hierarchical':
        return this.createHierarchicalChunks(content, chunkSize, overlap);
      case 'sliding':
        return this.createSlidingChunks(content, chunkSize, overlap);
      default:
        return this.createFixedChunks(content, chunkSize, overlap);
    }
  }

  /**
   * Criar chunks de tamanho fixo
   */
  private createFixedChunks(content: string, chunkSize: number, overlap: number): ContentChunk[] {
    const chunks: ContentChunk[] = [];
    const step = chunkSize - overlap;

    for (let i = 0; i < content.length; i += step) {
      const end = Math.min(i + chunkSize, content.length);
      const chunkContent = content.substring(i, end);

      if (chunkContent.trim().length > 0) {
        chunks.push({
          id: uuidv4(),
          content: chunkContent.trim(),
          index: chunks.length,
          startPosition: i,
          endPosition: end,
          metadata: {
            chunkType: 'fixed',
            size: chunkContent.length
          }
        });
      }
    }

    return chunks;
  }

  /**
   * Criar chunks semânticos (implementação básica)
   */
  private createSemanticChunks(content: string, chunkSize: number, overlap: number): ContentChunk[] {
    // Implementação básica baseada em parágrafos e sentenças
    const paragraphs = content.split(/\n\s*\n/);
    const chunks: ContentChunk[] = [];
    let currentChunk = '';
    let startPosition = 0;

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > chunkSize && currentChunk.length > 0) {
        // Salvar chunk atual
        chunks.push({
          id: uuidv4(),
          content: currentChunk.trim(),
          index: chunks.length,
          startPosition,
          endPosition: startPosition + currentChunk.length,
          metadata: {
            chunkType: 'semantic',
            paragraphs: currentChunk.split(/\n\s*\n/).length
          }
        });

        // Iniciar novo chunk com overlap
        const overlapText = currentChunk.substring(Math.max(0, currentChunk.length - overlap));
        startPosition += currentChunk.length - overlapText.length;
        currentChunk = overlapText + '\n\n' + paragraph;
      } else {
        if (currentChunk.length > 0) {
          currentChunk += '\n\n' + paragraph;
        } else {
          currentChunk = paragraph;
          startPosition = content.indexOf(paragraph);
        }
      }
    }

    // Adicionar último chunk se houver conteúdo
    if (currentChunk.trim().length > 0) {
      chunks.push({
        id: uuidv4(),
        content: currentChunk.trim(),
        index: chunks.length,
        startPosition,
        endPosition: startPosition + currentChunk.length,
        metadata: {
          chunkType: 'semantic',
          paragraphs: currentChunk.split(/\n\s*\n/).length
        }
      });
    }

    return chunks;
  }

  /**
   * Criar chunks hierárquicos
   */
  private createHierarchicalChunks(content: string, chunkSize: number, overlap: number): ContentChunk[] {
    // Implementação básica - poderia ser mais sofisticada
    return this.createSemanticChunks(content, chunkSize, overlap);
  }

  /**
   * Criar chunks deslizantes
   */
  private createSlidingChunks(content: string, chunkSize: number, overlap: number): ContentChunk[] {
    return this.createFixedChunks(content, chunkSize, overlap);
  }
}

// Instância singleton
export const documentIngestionService = new DocumentIngestionService();
