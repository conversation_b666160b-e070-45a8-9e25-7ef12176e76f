/**
 * Pipeline RAG Avançado
 * Integra todos os componentes do sistema RAG completo
 */

import { documentIngestionService, IngestionSource, IngestionResult } from './documentIngestion';
import { intelligentChunkingService, ChunkingStrategy, ChunkingResult } from './intelligentChunking';
import { advancedVectorStorageService } from './advancedVectorStorage';
import { hybridRetrievalService, HybridSearchQuery, HybridSearchResult } from './hybridRetrieval';
import { contextOptimizationService, OptimizedContext } from './contextOptimization';
import { geminiService } from './gemini';
import { rerankingService } from './rerankingService';
import { feedbackLearningService } from './feedbackLearning';

export interface RAGPipelineConfig {
  ingestion: {
    maxFileSize: number;
    supportedFormats: string[];
    enableOCR: boolean;
    enableMultimedia: boolean;
  };
  chunking: {
    strategy: ChunkingStrategy['type'];
    chunkSize: number;
    overlap: number;
    useSemanticChunking: boolean;
  };
  retrieval: {
    maxResults: number;
    threshold: number;
    fusionMethod: 'rrf' | 'weighted' | 'learned' | 'adaptive';
    enableHybridSearch: boolean;
    enableReranking: boolean;
  };
  generation: {
    maxTokens: number;
    temperature: number;
    enableContextOptimization: boolean;
    enableCitations: boolean;
  };
  learning: {
    enableFeedbackLearning: boolean;
    enableContinuousImprovement: boolean;
    collectImplicitFeedback: boolean;
  };
}

export interface RAGQuery {
  question: string;
  context?: string;
  sessionId?: string;
  userId?: string;
  filters?: Record<string, any>;
  options?: {
    maxResults?: number;
    includeExplanation?: boolean;
    includeCitations?: boolean;
    optimizeContext?: boolean;
    useReranking?: boolean;
  };
}

export interface RAGResponse {
  answer: string;
  sources: HybridSearchResult[];
  confidence: number;
  processingTime: number;
  pipeline: {
    retrievalTime: number;
    rerankingTime: number;
    contextOptimizationTime: number;
    generationTime: number;
  };
  metadata: {
    documentsFound: number;
    chunksProcessed: number;
    tokensUsed: number;
    compressionRatio?: number;
    qualityScore: number;
    usedStrategies: string[];
  };
  explanation?: string[];
  citations?: Array<{
    source: string;
    content: string;
    relevance: number;
  }>;
}

export interface PipelineMetrics {
  totalQueries: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  componentMetrics: {
    ingestion: any;
    retrieval: any;
    generation: any;
    learning: any;
  };
}

export class AdvancedRAGPipelineService {
  private config: RAGPipelineConfig;
  private metrics: PipelineMetrics;

  constructor(config?: Partial<RAGPipelineConfig>) {
    this.config = {
      ingestion: {
        maxFileSize: 50 * 1024 * 1024, // 50MB
        supportedFormats: ['pdf', 'docx', 'txt', 'xlsx', 'csv', 'json'],
        enableOCR: false,
        enableMultimedia: true
      },
      chunking: {
        strategy: 'semantic',
        chunkSize: 1000,
        overlap: 200,
        useSemanticChunking: true
      },
      retrieval: {
        maxResults: 10,
        threshold: 0.5,
        fusionMethod: 'rrf',
        enableHybridSearch: true,
        enableReranking: true
      },
      generation: {
        maxTokens: 8000,
        temperature: 0.7,
        enableContextOptimization: true,
        enableCitations: true
      },
      learning: {
        enableFeedbackLearning: true,
        enableContinuousImprovement: true,
        collectImplicitFeedback: true
      },
      ...config
    };

    this.initializeMetrics();
  }

  /**
   * Processar query completa através do pipeline RAG
   */
  async processQuery(query: RAGQuery): Promise<RAGResponse> {
    const startTime = Date.now();
    console.log(`🚀 Iniciando pipeline RAG para: "${query.question}"`);

    try {
      // 1. Retrieval híbrido
      const retrievalStartTime = Date.now();
      const searchQuery: HybridSearchQuery = {
        text: query.question,
        filters: query.filters,
        maxResults: query.options?.maxResults || this.config.retrieval.maxResults,
        fusionMethod: this.config.retrieval.fusionMethod,
        boostRecent: true,
        diversityFactor: 0.3
      };

      const retrievalResult = await hybridRetrievalService.search(searchQuery);
      const retrievalTime = Date.now() - retrievalStartTime;

      console.log(`📊 Retrieval: ${retrievalResult.results.length} documentos em ${retrievalTime}ms`);

      // 2. Reranking (se habilitado)
      let rerankedResults = retrievalResult.results;
      let rerankingTime = 0;

      if (this.config.retrieval.enableReranking && query.options?.useReranking !== false) {
        const rerankingStartTime = Date.now();
        rerankedResults = await rerankingService.rerank(query.question, retrievalResult.results);
        rerankingTime = Date.now() - rerankingStartTime;
        console.log(`🔄 Reranking: ${rerankingTime}ms`);
      }

      // 3. Otimização de contexto (se habilitado)
      let optimizedContext: OptimizedContext | null = null;
      let contextOptimizationTime = 0;

      if (this.config.generation.enableContextOptimization && query.options?.optimizeContext !== false) {
        const contextStartTime = Date.now();
        const chunks = rerankedResults.map(result => ({
          content: result.content,
          metadata: result.metadata,
          importance: result.scores.normalized
        }));

        optimizedContext = await contextOptimizationService.optimizeContext(chunks, query.question);
        contextOptimizationTime = Date.now() - contextStartTime;
        console.log(`🔧 Otimização de contexto: ${contextOptimizationTime}ms`);
      }

      // 4. Geração da resposta
      const generationStartTime = Date.now();
      const contextContent = optimizedContext?.content || 
        rerankedResults.map(r => r.content).join('\n\n');

      const answer = await this.generateAnswer(query.question, contextContent, query.context);
      const generationTime = Date.now() - generationStartTime;

      console.log(`💬 Geração: ${generationTime}ms`);

      // 5. Calcular confiança e métricas
      const confidence = this.calculateConfidence(rerankedResults, optimizedContext);
      const qualityScore = optimizedContext?.qualityScore || 0.8;

      // 6. Preparar citações (se habilitado)
      let citations: Array<{ source: string; content: string; relevance: number }> = [];
      if (this.config.generation.enableCitations && query.options?.includeCitations !== false) {
        citations = this.generateCitations(rerankedResults);
      }

      // 7. Preparar explicação (se solicitada)
      let explanation: string[] = [];
      if (query.options?.includeExplanation) {
        explanation = this.generateExplanation(retrievalResult, optimizedContext);
      }

      const totalTime = Date.now() - startTime;

      // 8. Registrar para aprendizado (se habilitado)
      if (this.config.learning.enableFeedbackLearning && query.sessionId) {
        await this.registerForLearning(query, {
          answer,
          sources: rerankedResults,
          confidence,
          processingTime: totalTime
        });
      }

      // 9. Atualizar métricas
      this.updateMetrics(totalTime, confidence, true);

      const response: RAGResponse = {
        answer,
        sources: rerankedResults,
        confidence,
        processingTime: totalTime,
        pipeline: {
          retrievalTime,
          rerankingTime,
          contextOptimizationTime,
          generationTime
        },
        metadata: {
          documentsFound: retrievalResult.results.length,
          chunksProcessed: optimizedContext?.chunks.length || rerankedResults.length,
          tokensUsed: optimizedContext?.tokensUsed || this.estimateTokens(contextContent),
          compressionRatio: optimizedContext?.compressionRatio,
          qualityScore,
          usedStrategies: retrievalResult.explanation || []
        },
        explanation,
        citations
      };

      console.log(`✅ Pipeline RAG concluído: ${totalTime}ms (confiança: ${(confidence * 100).toFixed(1)}%)`);
      return response;

    } catch (error: any) {
      console.error('❌ Erro no pipeline RAG:', error);
      this.updateMetrics(Date.now() - startTime, 0, false);
      
      throw new Error(`Erro no pipeline RAG: ${error.message}`);
    }
  }

  /**
   * Ingerir documentos no sistema
   */
  async ingestDocuments(sources: IngestionSource[]): Promise<{
    results: IngestionResult[];
    summary: {
      successful: number;
      failed: number;
      totalProcessingTime: number;
      documentsAdded: number;
      chunksCreated: number;
    };
  }> {
    console.log(`📥 Iniciando ingestão de ${sources.length} documentos...`);
    const startTime = Date.now();

    // 1. Ingestão e pré-processamento
    const ingestionResults = await documentIngestionService.ingestMultipleSources(sources);

    let documentsAdded = 0;
    let chunksCreated = 0;

    // 2. Chunking e indexação
    for (const result of ingestionResults) {
      if (result.success && result.extractedContent) {
        try {
          // Chunking inteligente
          const chunkingResult = await intelligentChunkingService.createIntelligentChunks(
            result.extractedContent.content,
            {
              type: this.config.chunking.strategy,
              chunkSize: this.config.chunking.chunkSize,
              overlap: this.config.chunking.overlap,
              useEmbeddings: this.config.chunking.useSemanticChunking
            },
            result.extractedContent.metadata
          );

          // Adicionar chunks ao armazenamento vetorial
          for (const chunk of chunkingResult.chunks) {
            await advancedVectorStorageService.addDocument(
              chunk.content,
              {
                title: `${result.extractedContent.metadata.title} - Chunk ${chunk.index}`,
                documentType: result.extractedContent.metadata.contentType,
                category: 'knowledge_base',
                tags: [],
                source: result.extractedContent.metadata.originalSource,
                chunkIndex: chunk.index,
                totalChunks: chunkingResult.totalChunks,
                parentDocumentId: result.contentId
              },
              chunk.embedding
            );
            chunksCreated++;
          }

          documentsAdded++;
          console.log(`✅ Documento processado: ${result.extractedContent.metadata.title} (${chunkingResult.totalChunks} chunks)`);

        } catch (error) {
          console.error(`❌ Erro ao processar documento ${result.contentId}:`, error);
        }
      }
    }

    const totalTime = Date.now() - startTime;
    const successful = ingestionResults.filter(r => r.success).length;
    const failed = ingestionResults.length - successful;

    console.log(`📊 Ingestão concluída: ${successful}/${ingestionResults.length} documentos, ${chunksCreated} chunks em ${totalTime}ms`);

    return {
      results: ingestionResults,
      summary: {
        successful,
        failed,
        totalProcessingTime: totalTime,
        documentsAdded,
        chunksCreated
      }
    };
  }

  /**
   * Gerar resposta usando LLM
   */
  private async generateAnswer(question: string, context: string, additionalContext?: string): Promise<string> {
    const fullContext = additionalContext ? `${additionalContext}\n\n${context}` : context;

    const prompt = `Você é Rafaela, uma assistente especializada em serviços públicos e legislação municipal.
Responda à pergunta usando as informações fornecidas no contexto. Seja precisa, útil e mantenha um tom profissional mas acessível.

Contexto:
${fullContext}

Pergunta: ${question}

Resposta:`;

    try {
      return await geminiService.generateContent(prompt);
    } catch (error) {
      console.error('Erro na geração da resposta:', error);
      return 'Desculpe, não foi possível gerar uma resposta no momento. Tente novamente.';
    }
  }

  /**
   * Calcular confiança da resposta
   */
  private calculateConfidence(results: HybridSearchResult[], optimizedContext?: OptimizedContext | null): number {
    if (results.length === 0) return 0;

    // Confiança baseada na qualidade dos resultados
    const avgSimilarity = results.reduce((sum, r) => sum + r.scores.normalized, 0) / results.length;

    // Confiança baseada na quantidade de resultados
    const quantityFactor = Math.min(1.0, results.length / 5);

    // Confiança baseada na otimização de contexto
    const contextFactor = optimizedContext?.qualityScore || 0.8;

    // Combinar fatores
    const confidence = (avgSimilarity * 0.5) + (quantityFactor * 0.2) + (contextFactor * 0.3);

    return Math.min(1.0, Math.max(0.0, confidence));
  }

  /**
   * Gerar citações
   */
  private generateCitations(results: HybridSearchResult[]): Array<{ source: string; content: string; relevance: number }> {
    return results.slice(0, 5).map(result => ({
      source: result.metadata.title || result.metadata.source || 'Documento',
      content: result.content.substring(0, 200) + '...',
      relevance: result.scores.normalized
    }));
  }

  /**
   * Gerar explicação do processo
   */
  private generateExplanation(retrievalResult: any, optimizedContext?: OptimizedContext | null): string[] {
    const explanation: string[] = [];

    explanation.push(`Busca realizada em ${retrievalResult.results.length} documentos`);

    if (retrievalResult.explanation) {
      explanation.push(...retrievalResult.explanation);
    }

    if (optimizedContext) {
      explanation.push(`Contexto otimizado: ${optimizedContext.optimizationMethods.join(', ')}`);
      explanation.push(`Compressão: ${(optimizedContext.compressionRatio * 100).toFixed(1)}%`);
    }

    return explanation;
  }

  /**
   * Registrar para aprendizado
   */
  private async registerForLearning(query: RAGQuery, response: any): Promise<void> {
    if (!this.config.learning.enableFeedbackLearning) return;

    try {
      await feedbackLearningService.registerInteraction({
        sessionId: query.sessionId!,
        userId: query.userId,
        query: query.question,
        response: response.answer,
        sources: response.sources,
        confidence: response.confidence,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.warn('Erro ao registrar para aprendizado:', error);
    }
  }

  /**
   * Estimar tokens
   */
  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  /**
   * Inicializar métricas
   */
  private initializeMetrics(): void {
    this.metrics = {
      totalQueries: 0,
      averageResponseTime: 0,
      averageConfidence: 0,
      successRate: 0,
      componentMetrics: {
        ingestion: {},
        retrieval: {},
        generation: {},
        learning: {}
      }
    };
  }

  /**
   * Atualizar métricas
   */
  private updateMetrics(responseTime: number, confidence: number, success: boolean): void {
    this.metrics.totalQueries++;
    this.metrics.averageResponseTime = (this.metrics.averageResponseTime + responseTime) / 2;
    this.metrics.averageConfidence = (this.metrics.averageConfidence + confidence) / 2;

    const successCount = this.metrics.successRate * (this.metrics.totalQueries - 1) + (success ? 1 : 0);
    this.metrics.successRate = successCount / this.metrics.totalQueries;
  }

  /**
   * Obter métricas do pipeline
   */
  getMetrics(): PipelineMetrics {
    return {
      ...this.metrics,
      componentMetrics: {
        ingestion: {},
        retrieval: hybridRetrievalService.getMetrics(),
        generation: {},
        learning: this.config.learning.enableFeedbackLearning ? {} : null
      }
    };
  }

  /**
   * Obter configuração atual
   */
  getConfig(): RAGPipelineConfig {
    return { ...this.config };
  }

  /**
   * Atualizar configuração
   */
  updateConfig(newConfig: Partial<RAGPipelineConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // Atualizar configurações dos componentes
    if (newConfig.generation?.maxTokens) {
      contextOptimizationService.setTokenLimits(newConfig.generation.maxTokens);
    }

    console.log('🔧 Configuração do pipeline RAG atualizada');
  }

  /**
   * Processar feedback do usuário
   */
  async processFeedback(sessionId: string, feedback: {
    rating: number;
    helpful: boolean;
    comment?: string;
    corrections?: string;
  }): Promise<void> {
    if (!this.config.learning.enableFeedbackLearning) return;

    try {
      await feedbackLearningService.processFeedback(sessionId, feedback);
      console.log(`📝 Feedback processado para sessão ${sessionId}`);
    } catch (error) {
      console.error('Erro ao processar feedback:', error);
    }
  }
}

// Instância singleton
export const advancedRAGPipelineService = new AdvancedRAGPipelineService();
