/**
 * Teste Básico do Pipeline RAG
 * Testa funcionalidades básicas sem dependências externas
 */

import { ragService } from '../services/ragService';
import * as fs from 'fs';
import * as path from 'path';

interface BasicTest {
  name: string;
  description: string;
  test: () => Promise<boolean>;
}

const basicTests: BasicTest[] = [
  {
    name: "Inicialização do RAG Service",
    description: "Verifica se o RAG Service inicializa corretamente",
    test: async () => {
      try {
        const metrics = ragService.getMetrics();
        console.log(`   ✅ Métricas obtidas:`, metrics);
        return true;
      } catch (error) {
        console.log(`   ❌ Erro:`, error);
        return false;
      }
    }
  },
  {
    name: "Verificação de Documentos de Teste",
    description: "Verifica se os documentos de teste existem",
    test: async () => {
      try {
        const testDocsDir = path.join(process.cwd(), 'test-documents');
        const files = [
          'servicos_municipais.txt',
          'projetos_lei.txt', 
          'agenda_eventos.txt'
        ];
        
        let allExist = true;
        for (const file of files) {
          const filePath = path.join(testDocsDir, file);
          if (!fs.existsSync(filePath)) {
            console.log(`   ❌ Arquivo não encontrado: ${file}`);
            allExist = false;
          } else {
            const stats = fs.statSync(filePath);
            console.log(`   ✅ ${file}: ${stats.size} bytes`);
          }
        }
        
        return allExist;
      } catch (error) {
        console.log(`   ❌ Erro:`, error);
        return false;
      }
    }
  },
  {
    name: "Teste de Query Simples",
    description: "Testa uma query básica sem processamento complexo",
    test: async () => {
      try {
        // Teste com query simples que não requer embeddings
        const response = await ragService.processQuery({
          question: "Teste básico",
          sessionId: "test_basic",
          maxResults: 1
        });
        
        console.log(`   ✅ Query processada`);
        console.log(`   - Resposta: ${response.answer.substring(0, 100)}...`);
        console.log(`   - Confiança: ${(response.confidence * 100).toFixed(1)}%`);
        console.log(`   - Tempo: ${response.processingTime}ms`);
        
        return response.answer.length > 0;
      } catch (error) {
        console.log(`   ❌ Erro na query:`, error);
        return false;
      }
    }
  },
  {
    name: "Teste de Métricas",
    description: "Verifica se as métricas são coletadas corretamente",
    test: async () => {
      try {
        const metrics = ragService.getMetrics();
        
        console.log(`   ✅ Métricas coletadas:`);
        console.log(`   - Total de queries: ${metrics.totalQueries || 0}`);
        console.log(`   - Taxa de sucesso: ${((metrics.successRate || 0) * 100).toFixed(1)}%`);
        console.log(`   - Tempo médio: ${(metrics.averageResponseTime || 0).toFixed(0)}ms`);
        console.log(`   - Confiança média: ${((metrics.averageConfidence || 0) * 100).toFixed(1)}%`);
        
        return true;
      } catch (error) {
        console.log(`   ❌ Erro nas métricas:`, error);
        return false;
      }
    }
  },
  {
    name: "Teste de Configuração",
    description: "Verifica se a configuração do pipeline está correta",
    test: async () => {
      try {
        // Verificar se o serviço tem configurações básicas
        const hasConfig = ragService.hasOwnProperty('config') || 
                         ragService.hasOwnProperty('pipeline') ||
                         ragService.hasOwnProperty('advancedPipeline');
        
        console.log(`   ✅ Configuração verificada`);
        console.log(`   - Serviço configurado: ${hasConfig ? 'Sim' : 'Não'}`);
        
        return true;
      } catch (error) {
        console.log(`   ❌ Erro na configuração:`, error);
        return false;
      }
    }
  }
];

async function runBasicTests(): Promise<void> {
  console.log('🧪 INICIANDO TESTES BÁSICOS DO PIPELINE RAG\n');
  console.log('==========================================\n');

  const results = {
    total: basicTests.length,
    passed: 0,
    failed: 0,
    details: [] as any[]
  };

  for (let i = 0; i < basicTests.length; i++) {
    const test = basicTests[i];
    console.log(`📝 Teste ${i + 1}/${basicTests.length}: ${test.name}`);
    console.log(`   Descrição: ${test.description}`);

    try {
      const startTime = Date.now();
      const passed = await test.test();
      const duration = Date.now() - startTime;

      if (passed) {
        console.log(`   ✅ PASSOU (${duration}ms)\n`);
        results.passed++;
      } else {
        console.log(`   ❌ FALHOU (${duration}ms)\n`);
        results.failed++;
      }

      results.details.push({
        name: test.name,
        passed,
        duration,
        description: test.description
      });

    } catch (error) {
      console.log(`   ❌ ERRO: ${error}\n`);
      results.failed++;
      
      results.details.push({
        name: test.name,
        passed: false,
        duration: 0,
        error: String(error),
        description: test.description
      });
    }
  }

  // Resumo final
  console.log('📊 RESUMO DOS TESTES BÁSICOS');
  console.log('============================');
  console.log(`Total de testes: ${results.total}`);
  console.log(`Testes aprovados: ${results.passed} (${(results.passed/results.total*100).toFixed(1)}%)`);
  console.log(`Testes reprovados: ${results.failed} (${(results.failed/results.total*100).toFixed(1)}%)`);
  console.log('');

  // Status geral
  const successRate = results.passed / results.total;
  
  if (successRate >= 0.8) {
    console.log('🎉 SISTEMA FUNCIONANDO CORRETAMENTE!');
    console.log('   O Pipeline RAG está operacional e pronto para testes avançados.');
  } else if (successRate >= 0.6) {
    console.log('⚠️ SISTEMA PARCIALMENTE FUNCIONAL');
    console.log('   Algumas funcionalidades precisam de ajustes.');
  } else {
    console.log('❌ SISTEMA COM PROBLEMAS');
    console.log('   Várias funcionalidades precisam de correção.');
  }

  console.log(`\nScore geral: ${(successRate * 100).toFixed(1)}%`);
  
  // Detalhes dos testes que falharam
  const failedTests = results.details.filter(t => !t.passed);
  if (failedTests.length > 0) {
    console.log('\n🔍 TESTES QUE FALHARAM:');
    failedTests.forEach(test => {
      console.log(`   - ${test.name}: ${test.error || 'Falha no teste'}`);
    });
  }

  console.log('\n🚀 PRÓXIMOS PASSOS:');
  if (successRate >= 0.8) {
    console.log('   1. Configurar GEMINI_API_KEY para testes avançados');
    console.log('   2. Executar testes de qualidade com documentos reais');
    console.log('   3. Testar integração com WhatsApp');
  } else {
    console.log('   1. Corrigir problemas identificados nos testes');
    console.log('   2. Verificar configurações do ambiente');
    console.log('   3. Executar testes básicos novamente');
  }
}

// Executar testes se chamado diretamente
if (require.main === module) {
  (async () => {
    try {
      await runBasicTests();
      process.exit(0);
    } catch (error) {
      console.error('❌ Erro nos testes básicos:', error);
      process.exit(1);
    }
  })();
}

export { runBasicTests };
