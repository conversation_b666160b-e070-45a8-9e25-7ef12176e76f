/**
 * Serviço de Retrieval Híbrido
 * Pesquis<PERSON> paralela (vetorial, BM25, grafo, temporal) com fusões por rank
 */

import { v4 as uuidv4 } from 'uuid';
import { advancedVectorStorageService, VectorSearchResult } from './advancedVectorStorage';
import { embeddingService } from './embeddingService';
import { chromaVectorDB } from './chromaVectorDB';

export interface HybridSearchQuery {
  text: string;
  filters?: Record<string, any>;
  searchStrategies?: SearchStrategy[];
  fusionMethod?: 'rrf' | 'weighted' | 'learned' | 'adaptive';
  maxResults?: number;
  threshold?: number;
  boostRecent?: boolean;
  diversityFactor?: number;
}

export interface SearchStrategy {
  type: 'vector' | 'bm25' | 'graph' | 'temporal' | 'semantic' | 'fuzzy';
  weight: number;
  enabled: boolean;
  config?: Record<string, any>;
}

export interface HybridSearchResult {
  id: string;
  content: string;
  metadata: any;
  scores: {
    vector?: number;
    bm25?: number;
    graph?: number;
    temporal?: number;
    semantic?: number;
    fuzzy?: number;
    combined: number;
    normalized: number;
  };
  rank: number;
  relevanceExplanation?: string[];
}

export interface SearchMetrics {
  totalTime: number;
  strategyTimes: Record<string, number>;
  resultsPerStrategy: Record<string, number>;
  fusionTime: number;
  cacheHits: number;
  totalQueries: number;
}

export class HybridRetrievalService {
  private searchCache: Map<string, HybridSearchResult[]> = new Map();
  private documentGraph: Map<string, Set<string>> = new Map(); // Grafo de relacionamentos
  private termIndex: Map<string, Set<string>> = new Map(); // Índice invertido para BM25
  private metrics: SearchMetrics;

  constructor() {
    this.initializeMetrics();
    this.buildIndices();
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalTime: 0,
      strategyTimes: {},
      resultsPerStrategy: {},
      fusionTime: 0,
      cacheHits: 0,
      totalQueries: 0
    };
  }

  /**
   * Busca híbrida principal
   */
  async search(query: HybridSearchQuery): Promise<{
    results: HybridSearchResult[];
    metrics: SearchMetrics;
    explanation: string[];
  }> {
    const startTime = Date.now();
    this.metrics.totalQueries++;

    console.log(`🔍 Iniciando busca híbrida: "${query.text}"`);

    // Verificar cache
    const cacheKey = this.generateCacheKey(query);
    if (this.searchCache.has(cacheKey)) {
      this.metrics.cacheHits++;
      console.log('💾 Resultado obtido do cache');
      return {
        results: this.searchCache.get(cacheKey)!,
        metrics: this.metrics,
        explanation: ['Resultado obtido do cache']
      };
    }

    // Estratégias padrão se não especificadas
    const strategies = query.searchStrategies || this.getDefaultStrategies();
    const explanation: string[] = [];

    // Executar estratégias de busca em paralelo
    const searchPromises = strategies
      .filter(strategy => strategy.enabled)
      .map(strategy => this.executeStrategy(query.text, strategy, query.filters));

    const strategyResults = await Promise.all(searchPromises);

    // Combinar resultados usando método de fusão especificado
    const fusionStartTime = Date.now();
    const combinedResults = await this.fuseResults(
      strategyResults,
      strategies.filter(s => s.enabled),
      query.fusionMethod || 'rrf'
    );
    this.metrics.fusionTime = Date.now() - fusionStartTime;

    // Aplicar filtros adicionais
    let filteredResults = combinedResults;
    if (query.filters) {
      filteredResults = this.applyFilters(combinedResults, query.filters);
      explanation.push(`Filtros aplicados: ${Object.keys(query.filters).join(', ')}`);
    }

    // Aplicar boost temporal se habilitado
    if (query.boostRecent) {
      filteredResults = this.applyTemporalBoost(filteredResults);
      explanation.push('Boost temporal aplicado para documentos recentes');
    }

    // Aplicar diversificação se especificada
    if (query.diversityFactor && query.diversityFactor > 0) {
      filteredResults = await this.diversifyResults(filteredResults, query.diversityFactor);
      explanation.push(`Diversificação aplicada (fator: ${query.diversityFactor})`);
    }

    // Limitar resultados
    const maxResults = query.maxResults || 10;
    const finalResults = filteredResults.slice(0, maxResults);

    // Adicionar explicações de relevância
    finalResults.forEach((result, index) => {
      result.rank = index + 1;
      result.relevanceExplanation = this.generateRelevanceExplanation(result, strategies);
    });

    // Armazenar no cache
    this.searchCache.set(cacheKey, finalResults);

    // Atualizar métricas
    this.metrics.totalTime = Date.now() - startTime;

    explanation.push(`Busca concluída: ${finalResults.length} resultados em ${this.metrics.totalTime}ms`);

    console.log(`✅ Busca híbrida concluída: ${finalResults.length} resultados em ${this.metrics.totalTime}ms`);

    return {
      results: finalResults,
      metrics: this.metrics,
      explanation
    };
  }

  /**
   * Executar estratégia de busca específica
   */
  private async executeStrategy(
    query: string,
    strategy: SearchStrategy,
    filters?: Record<string, any>
  ): Promise<{ strategy: SearchStrategy; results: any[] }> {
    const startTime = Date.now();

    try {
      let results: any[] = [];

      switch (strategy.type) {
        case 'vector':
          results = await this.vectorSearch(query, strategy.config, filters);
          break;
        case 'bm25':
          results = await this.bm25Search(query, strategy.config, filters);
          break;
        case 'graph':
          results = await this.graphSearch(query, strategy.config, filters);
          break;
        case 'temporal':
          results = await this.temporalSearch(query, strategy.config, filters);
          break;
        case 'semantic':
          results = await this.semanticSearch(query, strategy.config, filters);
          break;
        case 'fuzzy':
          results = await this.fuzzySearch(query, strategy.config, filters);
          break;
      }

      const executionTime = Date.now() - startTime;
      this.metrics.strategyTimes[strategy.type] = executionTime;
      this.metrics.resultsPerStrategy[strategy.type] = results.length;

      console.log(`📊 ${strategy.type} search: ${results.length} resultados em ${executionTime}ms`);

      return { strategy, results };
    } catch (error) {
      console.error(`Erro na estratégia ${strategy.type}:`, error);
      return { strategy, results: [] };
    }
  }

  /**
   * Busca vetorial
   */
  private async vectorSearch(query: string, config?: Record<string, any>, filters?: Record<string, any>): Promise<VectorSearchResult[]> {
    return advancedVectorStorageService.searchSimilar(query, {
      limit: config?.limit || 20,
      threshold: config?.threshold || 0.5,
      filter: filters,
      searchType: 'similarity'
    });
  }

  /**
   * Busca BM25
   */
  private async bm25Search(query: string, config?: Record<string, any>, filters?: Record<string, any>): Promise<any[]> {
    const queryTerms = this.tokenize(query.toLowerCase());
    const results: Array<{ id: string; score: number; content: string; metadata: any }> = [];
    
    // Parâmetros BM25
    const k1 = config?.k1 || 1.2;
    const b = config?.b || 0.75;
    const avgDocLength = this.calculateAverageDocumentLength();

    for (const [docId, terms] of this.termIndex) {
      let score = 0;
      const docLength = terms.size;

      for (const term of queryTerms) {
        const termFreq = this.getTermFrequency(docId, term);
        const docFreq = this.getDocumentFrequency(term);
        
        if (termFreq > 0) {
          const idf = Math.log((this.termIndex.size - docFreq + 0.5) / (docFreq + 0.5));
          const tf = (termFreq * (k1 + 1)) / (termFreq + k1 * (1 - b + b * (docLength / avgDocLength)));
          score += idf * tf;
        }
      }

      if (score > 0) {
        // Buscar documento completo
        const doc = await this.getDocumentById(docId);
        if (doc) {
          results.push({
            id: docId,
            score,
            content: doc.content,
            metadata: doc.metadata
          });
        }
      }
    }

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, config?.limit || 20);
  }

  /**
   * Busca por grafo de relacionamentos
   */
  private async graphSearch(query: string, config?: Record<string, any>, filters?: Record<string, any>): Promise<any[]> {
    // Primeiro, encontrar documentos iniciais via busca vetorial
    const initialResults = await this.vectorSearch(query, { limit: 5 }, filters);
    const results: Array<{ id: string; score: number; content: string; metadata: any }> = [];

    // Expandir busca através do grafo
    for (const result of initialResults) {
      const relatedDocs = this.documentGraph.get(result.id) || new Set();
      
      for (const relatedId of relatedDocs) {
        const doc = await this.getDocumentById(relatedId);
        if (doc) {
          // Score baseado na distância no grafo e similaridade inicial
          const graphScore = result.similarity * 0.8; // Penalizar por distância
          results.push({
            id: relatedId,
            score: graphScore,
            content: doc.content,
            metadata: doc.metadata
          });
        }
      }
    }

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, config?.limit || 20);
  }

  /**
   * Busca temporal
   */
  private async temporalSearch(query: string, config?: Record<string, any>, filters?: Record<string, any>): Promise<any[]> {
    const vectorResults = await this.vectorSearch(query, config, filters);
    const now = Date.now();
    const maxAge = config?.maxAge || (365 * 24 * 60 * 60 * 1000); // 1 ano

    return vectorResults.map(result => {
      const timestamp = new Date(result.metadata.timestamp).getTime();
      const age = now - timestamp;
      const temporalScore = Math.max(0, 1 - (age / maxAge));
      
      return {
        ...result,
        score: result.similarity * (0.7 + 0.3 * temporalScore), // Boost temporal
        temporalScore
      };
    }).sort((a, b) => b.score - a.score);
  }

  /**
   * Busca semântica avançada
   */
  private async semanticSearch(query: string, config?: Record<string, any>, filters?: Record<string, any>): Promise<any[]> {
    // Expandir query com sinônimos e termos relacionados
    const expandedQuery = await this.expandQuery(query);
    
    // Buscar com query expandida
    return this.vectorSearch(expandedQuery, config, filters);
  }

  /**
   * Busca fuzzy para lidar com erros de digitação
   */
  private async fuzzySearch(query: string, config?: Record<string, any>, filters?: Record<string, any>): Promise<any[]> {
    const queryTerms = this.tokenize(query.toLowerCase());
    const results: Array<{ id: string; score: number; content: string; metadata: any }> = [];
    const maxDistance = config?.maxDistance || 2;

    for (const [docId, terms] of this.termIndex) {
      let score = 0;

      for (const queryTerm of queryTerms) {
        for (const docTerm of terms) {
          const distance = this.levenshteinDistance(queryTerm, docTerm);
          if (distance <= maxDistance) {
            const similarity = 1 - (distance / Math.max(queryTerm.length, docTerm.length));
            score += similarity;
          }
        }
      }

      if (score > 0) {
        const doc = await this.getDocumentById(docId);
        if (doc) {
          results.push({
            id: docId,
            score,
            content: doc.content,
            metadata: doc.metadata
          });
        }
      }
    }

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, config?.limit || 20);
  }

  /**
   * Fusão de resultados usando diferentes métodos
   */
  private async fuseResults(
    strategyResults: Array<{ strategy: SearchStrategy; results: any[] }>,
    strategies: SearchStrategy[],
    fusionMethod: string
  ): Promise<HybridSearchResult[]> {
    switch (fusionMethod) {
      case 'rrf':
        return this.reciprocalRankFusion(strategyResults, strategies);
      case 'weighted':
        return this.weightedFusion(strategyResults, strategies);
      case 'learned':
        return this.learnedFusion(strategyResults, strategies);
      case 'adaptive':
        return this.adaptiveFusion(strategyResults, strategies);
      default:
        return this.reciprocalRankFusion(strategyResults, strategies);
    }
  }

  /**
   * Reciprocal Rank Fusion (RRF)
   */
  private reciprocalRankFusion(
    strategyResults: Array<{ strategy: SearchStrategy; results: any[] }>,
    strategies: SearchStrategy[]
  ): HybridSearchResult[] {
    const k = 60; // Parâmetro RRF padrão
    const documentScores = new Map<string, { scores: Record<string, number>; doc: any }>();

    // Calcular scores RRF para cada documento
    strategyResults.forEach(({ strategy, results }) => {
      results.forEach((result, rank) => {
        const docId = result.id;
        const rrfScore = 1 / (k + rank + 1);

        if (!documentScores.has(docId)) {
          documentScores.set(docId, {
            scores: {},
            doc: result
          });
        }

        const docData = documentScores.get(docId)!;
        docData.scores[strategy.type] = rrfScore;
      });
    });

    // Combinar scores e criar resultados finais
    const hybridResults: HybridSearchResult[] = [];

    for (const [docId, { scores, doc }] of documentScores) {
      const combinedScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
      const normalizedScore = combinedScore / strategies.length;

      hybridResults.push({
        id: docId,
        content: doc.content,
        metadata: doc.metadata,
        scores: {
          ...scores,
          combined: combinedScore,
          normalized: normalizedScore
        },
        rank: 0 // Será definido após ordenação
      });
    }

    return hybridResults.sort((a, b) => b.scores.combined - a.scores.combined);
  }

  /**
   * Fusão ponderada
   */
  private weightedFusion(
    strategyResults: Array<{ strategy: SearchStrategy; results: any[] }>,
    strategies: SearchStrategy[]
  ): HybridSearchResult[] {
    const documentScores = new Map<string, { scores: Record<string, number>; doc: any }>();

    // Calcular scores ponderados
    strategyResults.forEach(({ strategy, results }) => {
      results.forEach(result => {
        const docId = result.id;
        const score = (result.score || result.similarity || 0) * strategy.weight;

        if (!documentScores.has(docId)) {
          documentScores.set(docId, {
            scores: {},
            doc: result
          });
        }

        const docData = documentScores.get(docId)!;
        docData.scores[strategy.type] = score;
      });
    });

    // Combinar scores ponderados
    const hybridResults: HybridSearchResult[] = [];

    for (const [docId, { scores, doc }] of documentScores) {
      const combinedScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
      const totalWeight = strategies.reduce((sum, s) => sum + s.weight, 0);
      const normalizedScore = combinedScore / totalWeight;

      hybridResults.push({
        id: docId,
        content: doc.content,
        metadata: doc.metadata,
        scores: {
          ...scores,
          combined: combinedScore,
          normalized: normalizedScore
        },
        rank: 0
      });
    }

    return hybridResults.sort((a, b) => b.scores.combined - a.scores.combined);
  }

  /**
   * Fusão aprendida (implementação simplificada)
   */
  private learnedFusion(
    strategyResults: Array<{ strategy: SearchStrategy; results: any[] }>,
    strategies: SearchStrategy[]
  ): HybridSearchResult[] {
    // Por enquanto, usar fusão ponderada com pesos adaptativos
    const adaptedStrategies = strategies.map(strategy => ({
      ...strategy,
      weight: this.calculateAdaptiveWeight(strategy.type)
    }));

    return this.weightedFusion(strategyResults, adaptedStrategies);
  }

  /**
   * Fusão adaptativa baseada na performance das estratégias
   */
  private adaptiveFusion(
    strategyResults: Array<{ strategy: SearchStrategy; results: any[] }>,
    strategies: SearchStrategy[]
  ): HybridSearchResult[] {
    // Ajustar pesos baseado na performance histórica
    const adaptedStrategies = strategies.map(strategy => {
      const historicalPerformance = this.getStrategyPerformance(strategy.type);
      const adaptiveWeight = strategy.weight * (1 + historicalPerformance);

      return {
        ...strategy,
        weight: Math.max(0.1, Math.min(2.0, adaptiveWeight))
      };
    });

    return this.weightedFusion(strategyResults, adaptedStrategies);
  }

  /**
   * Aplicar filtros aos resultados
   */
  private applyFilters(results: HybridSearchResult[], filters: Record<string, any>): HybridSearchResult[] {
    return results.filter(result => {
      for (const [key, value] of Object.entries(filters)) {
        const metadataValue = result.metadata[key];

        if (Array.isArray(value)) {
          if (!value.includes(metadataValue)) return false;
        } else if (typeof value === 'string') {
          if (metadataValue !== value) return false;
        } else if (typeof value === 'object' && value !== null) {
          if (value.min !== undefined && metadataValue < value.min) return false;
          if (value.max !== undefined && metadataValue > value.max) return false;
          if (value.regex && !new RegExp(value.regex).test(metadataValue)) return false;
        }
      }
      return true;
    });
  }

  /**
   * Métodos auxiliares
   */
  private getDefaultStrategies(): SearchStrategy[] {
    return [
      { type: 'vector', weight: 0.4, enabled: true },
      { type: 'bm25', weight: 0.3, enabled: true },
      { type: 'semantic', weight: 0.2, enabled: true },
      { type: 'temporal', weight: 0.1, enabled: true }
    ];
  }

  private generateCacheKey(query: HybridSearchQuery): string {
    return `${query.text}_${JSON.stringify(query.filters)}_${query.fusionMethod}_${query.maxResults}`;
  }

  private tokenize(text: string): string[] {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 2);
  }

  private calculateAverageDocumentLength(): number {
    if (this.termIndex.size === 0) return 100;

    let totalLength = 0;
    for (const terms of this.termIndex.values()) {
      totalLength += terms.size;
    }
    return totalLength / this.termIndex.size;
  }

  private getTermFrequency(docId: string, term: string): number {
    // Implementação simplificada - em produção usaria índice mais eficiente
    const terms = this.termIndex.get(docId);
    return terms?.has(term) ? 1 : 0;
  }

  private getDocumentFrequency(term: string): number {
    let count = 0;
    for (const terms of this.termIndex.values()) {
      if (terms.has(term)) count++;
    }
    return count;
  }

  private async getDocumentById(docId: string): Promise<any> {
    // Implementação simplificada - buscar no armazenamento vetorial
    try {
      const stats = advancedVectorStorageService.getDetailedStats();
      // Retornar documento mock para demonstração
      return {
        id: docId,
        content: `Documento ${docId}`,
        metadata: { timestamp: new Date().toISOString() }
      };
    } catch (error) {
      return null;
    }
  }

  private async expandQuery(query: string): Promise<string> {
    // Implementação básica de expansão de query
    // Em produção, usaria modelos de linguagem ou bases de sinônimos
    const synonyms: Record<string, string[]> = {
      'saúde': ['medicina', 'médico', 'hospital', 'tratamento'],
      'educação': ['escola', 'ensino', 'professor', 'aluno'],
      'transporte': ['ônibus', 'metrô', 'trânsito', 'mobilidade']
    };

    let expandedQuery = query;
    for (const [term, syns] of Object.entries(synonyms)) {
      if (query.toLowerCase().includes(term)) {
        expandedQuery += ' ' + syns.join(' ');
      }
    }

    return expandedQuery;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  private calculateAdaptiveWeight(strategyType: string): number {
    // Implementação básica - em produção usaria dados históricos
    const baseWeights: Record<string, number> = {
      vector: 0.4,
      bm25: 0.3,
      semantic: 0.2,
      temporal: 0.1,
      graph: 0.15,
      fuzzy: 0.05
    };

    return baseWeights[strategyType] || 0.1;
  }

  private getStrategyPerformance(strategyType: string): number {
    // Implementação básica - retornar performance mock
    const performance = this.metrics.resultsPerStrategy[strategyType] || 0;
    return Math.min(0.5, performance / 100); // Normalizar entre 0 e 0.5
  }

  private async calculateContentSimilarity(content1: string, content2: string): Promise<number> {
    try {
      const embedding1 = await embeddingService.generateEmbedding(content1);
      const embedding2 = await embeddingService.generateEmbedding(content2);

      return this.cosineSimilarity(embedding1.embedding, embedding2.embedding);
    } catch (error) {
      // Fallback para similaridade baseada em palavras
      const words1 = new Set(this.tokenize(content1));
      const words2 = new Set(this.tokenize(content2));
      const intersection = new Set([...words1].filter(x => words2.has(x)));
      const union = new Set([...words1, ...words2]);

      return intersection.size / union.size;
    }
  }

  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }

  private generateRelevanceExplanation(result: HybridSearchResult, strategies: SearchStrategy[]): string[] {
    const explanations: string[] = [];

    for (const strategy of strategies) {
      const score = result.scores[strategy.type];
      if (score && score > 0) {
        explanations.push(`${strategy.type}: ${(score * 100).toFixed(1)}%`);
      }
    }

    explanations.push(`Score combinado: ${(result.scores.normalized * 100).toFixed(1)}%`);

    return explanations;
  }

  private buildIndices(): void {
    // Implementação básica para construir índices
    console.log('🔧 Construindo índices para retrieval híbrido...');

    // Em produção, construiria índices a partir dos documentos existentes
    // Por enquanto, inicializar estruturas vazias

    console.log('✅ Índices construídos');
  }

  /**
   * Obter métricas do sistema
   */
  getMetrics(): SearchMetrics {
    return { ...this.metrics };
  }

  /**
   * Limpar cache
   */
  clearCache(): void {
    this.searchCache.clear();
    console.log('🧹 Cache de busca híbrida limpo');
  }
}

// Instância singleton
export const hybridRetrievalService = new HybridRetrievalService();
