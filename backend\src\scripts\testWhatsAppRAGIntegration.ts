/**
 * Teste de Integração WhatsApp + Pipeline RAG Avançado
 * Simula conversas reais para testar a qualidade das respostas
 */

import { ragService } from '../services/ragService';
import { geminiService } from '../services/gemini';
import { IContact } from '../models/Contact';
import * as fs from 'fs';
import * as path from 'path';

// Mock de contatos para teste
const mockContacts: IContact[] = [
  {
    name: '<PERSON>',
    phone: '84988501582',
    babyGender: 'female',
    registrationStatus: 'registered',
    isActive: true,
    lastInteraction: new Date(),
    evaluationMessages: 0,
    interestScore: 0.8,
    createdAt: new Date(),
    updatedAt: new Date(),
    updateInteraction: async () => {},
    incrementEvaluationMessages: async () => {},
    updateInterestScore: async () => {},
    markAsRegistered: async () => {},
    markAsNotInterested: async () => {},
    deactivate: async () => {},
    reactivate: async () => {}
  },
  {
    name: '<PERSON>',
    phone: '84987654321',
    babyGender: 'male',
    registrationStatus: 'registered',
    isActive: true,
    lastInteraction: new Date(),
    evaluationMessages: 0,
    interestScore: 0.9,
    createdAt: new Date(),
    updatedAt: new Date(),
    updateInteraction: async () => {},
    incrementEvaluationMessages: async () => {},
    updateInterestScore: async () => {},
    markAsRegistered: async () => {},
    markAsNotInterested: async () => {},
    deactivate: async () => {},
    reactivate: async () => {}
  },
  {
    name: 'Ana Costa',
    phone: '84999887766',
    babyGender: 'unknown',
    registrationStatus: 'unregistered',
    isActive: true,
    lastInteraction: new Date(),
    evaluationMessages: 0,
    interestScore: 0.3,
    createdAt: new Date(),
    updatedAt: new Date(),
    updateInteraction: async () => {},
    incrementEvaluationMessages: async () => {},
    updateInterestScore: async () => {},
    markAsRegistered: async () => {},
    markAsNotInterested: async () => {},
    deactivate: async () => {},
    reactivate: async () => {}
  }
];

// Cenários de teste realistas
const testScenarios = [
  {
    contact: mockContacts[0],
    message: "Como faço para solicitar alvará de funcionamento?",
    expectedRAG: true,
    description: "Pergunta sobre serviços municipais"
  },
  {
    contact: mockContacts[1],
    message: "Quais são os projetos da vereadora para gestantes?",
    expectedRAG: true,
    description: "Pergunta sobre projetos de lei"
  },
  {
    contact: mockContacts[0],
    message: "Qual o horário de atendimento do gabinete?",
    expectedRAG: true,
    description: "Pergunta sobre atendimento"
  },
  {
    contact: mockContacts[2],
    message: "Oi, bom dia! Como você pode me ajudar?",
    expectedRAG: false,
    description: "Saudação de pessoa não registrada"
  },
  {
    contact: mockContacts[1],
    message: "Preciso de ajuda com o auxílio maternidade",
    expectedRAG: true,
    description: "Pergunta sobre programa específico"
  },
  {
    contact: mockContacts[0],
    message: "Onde fica a UBS mais próxima?",
    expectedRAG: true,
    description: "Pergunta sobre serviços de saúde"
  },
  {
    contact: mockContacts[1],
    message: "Como participar do programa mulher empreendedora?",
    expectedRAG: true,
    description: "Pergunta sobre programa social"
  },
  {
    contact: mockContacts[0],
    message: "Obrigada pela ajuda!",
    expectedRAG: false,
    description: "Agradecimento simples"
  }
];

interface TestResult {
  scenario: string;
  contact: string;
  message: string;
  response: string;
  usedRAG: boolean;
  expectedRAG: boolean;
  responseTime: number;
  qualityScore: number;
  issues: string[];
}

async function ingestTestDocuments(): Promise<boolean> {
  console.log('📥 Ingerindo documentos de teste para WhatsApp...');
  
  const testDocsDir = path.join(process.cwd(), 'test-documents');
  
  if (!fs.existsSync(testDocsDir)) {
    console.log('❌ Diretório test-documents não encontrado');
    return false;
  }

  const sources = [
    {
      type: 'file' as const,
      source: path.join(testDocsDir, 'servicos_municipais.txt'),
      metadata: {
        category: 'servicos',
        documentType: 'manual',
        title: 'Serviços Municipais',
        tags: ['alvará', 'licença', 'saúde', 'educação', 'transporte', 'atendimento']
      }
    },
    {
      type: 'file' as const,
      source: path.join(testDocsDir, 'projetos_lei.txt'),
      metadata: {
        category: 'legislacao',
        documentType: 'projeto_lei',
        title: 'Projetos de Lei da Vereadora Rafaela',
        tags: ['maternidade', 'primeira_infancia', 'empreendedorismo', 'violencia_domestica', 'auxilio']
      }
    },
    {
      type: 'file' as const,
      source: path.join(testDocsDir, 'agenda_eventos.txt'),
      metadata: {
        category: 'agenda',
        documentType: 'cronograma',
        title: 'Agenda e Eventos',
        tags: ['reuniões', 'eventos', 'atendimento', 'projetos', 'horarios']
      }
    }
  ];

  try {
    const result = await ragService.ingestDocuments(sources);
    console.log(`✅ Documentos ingeridos: ${result.successful}/${sources.length}`);
    console.log(`📊 Chunks criados: ${result.chunksCreated}`);
    return result.successful === sources.length;
  } catch (error) {
    console.error('❌ Erro na ingestão:', error);
    return false;
  }
}

async function testWhatsAppRAGIntegration(): Promise<void> {
  console.log('🧪 TESTE DE INTEGRAÇÃO WHATSAPP + PIPELINE RAG AVANÇADO\n');
  console.log('=======================================================\n');

  // Primeiro, ingerir documentos
  const ingestionSuccess = await ingestTestDocuments();
  if (!ingestionSuccess) {
    console.log('❌ Falha na ingestão de documentos. Continuando com testes...\n');
  }

  // Aguardar processamento
  console.log('⏳ Aguardando processamento dos documentos...\n');
  await new Promise(resolve => setTimeout(resolve, 3000));

  const results: TestResult[] = [];

  for (let i = 0; i < testScenarios.length; i++) {
    const scenario = testScenarios[i];
    console.log(`📱 Teste ${i + 1}/${testScenarios.length}: ${scenario.description}`);
    console.log(`👤 Contato: ${scenario.contact.name} (${scenario.contact.registrationStatus})`);
    console.log(`💬 Mensagem: "${scenario.message}"`);

    try {
      const startTime = Date.now();
      
      // Simular processamento WhatsApp com RAG
      const response = await geminiService.generateRAGEnhancedResponse(
        scenario.contact,
        scenario.message
      );

      const responseTime = Date.now() - startTime;
      
      // Analisar qualidade da resposta
      const qualityAnalysis = analyzeResponseQuality(response, scenario);
      
      console.log(`✅ Resposta (${responseTime}ms):`);
      console.log(`   "${response}"`);
      console.log(`📊 Análise:`);
      console.log(`   - Usou RAG: ${qualityAnalysis.usedRAG ? '✅' : '❌'} (esperado: ${scenario.expectedRAG ? 'Sim' : 'Não'})`);
      console.log(`   - Personalizada: ${qualityAnalysis.isPersonalized ? '✅' : '❌'}`);
      console.log(`   - Tem assinatura: ${qualityAnalysis.hasSignature ? '✅' : '❌'}`);
      console.log(`   - Tamanho adequado: ${qualityAnalysis.hasGoodLength ? '✅' : '❌'} (${response.length} chars)`);
      console.log(`   - Score de qualidade: ${(qualityAnalysis.qualityScore * 100).toFixed(1)}%`);
      
      if (qualityAnalysis.issues.length > 0) {
        console.log(`⚠️ Problemas: ${qualityAnalysis.issues.join(', ')}`);
      }

      results.push({
        scenario: scenario.description,
        contact: scenario.contact.name,
        message: scenario.message,
        response,
        usedRAG: qualityAnalysis.usedRAG,
        expectedRAG: scenario.expectedRAG,
        responseTime,
        qualityScore: qualityAnalysis.qualityScore,
        issues: qualityAnalysis.issues
      });

      console.log('');

    } catch (error) {
      console.error(`❌ Erro no teste: ${error}`);
      console.log('');
    }
  }

  // Gerar relatório final
  generateFinalReport(results);
}

function analyzeResponseQuality(response: string, scenario: any): {
  usedRAG: boolean;
  isPersonalized: boolean;
  hasSignature: boolean;
  hasGoodLength: boolean;
  qualityScore: number;
  issues: string[];
} {
  const issues: string[] = [];
  let qualityScore = 0;

  // Verificar se usou RAG (baseado no conteúdo da resposta)
  const usedRAG = response.includes('R$') || 
                  response.includes('dias') || 
                  response.includes('horário') ||
                  response.includes('telefone') ||
                  response.includes('endereço') ||
                  response.includes('projeto') ||
                  response.includes('programa');

  // Verificar personalização
  const isPersonalized = response.toLowerCase().includes(scenario.contact.name.toLowerCase().split(' ')[0]);
  
  // Verificar assinatura
  const hasSignature = response.includes('🧡');
  
  // Verificar tamanho
  const hasGoodLength = response.length >= 20 && response.length <= 300;

  // Calcular score
  if (usedRAG === scenario.expectedRAG) qualityScore += 0.3;
  else issues.push('RAG não usado conforme esperado');

  if (isPersonalized) qualityScore += 0.2;
  else issues.push('Resposta não personalizada');

  if (hasSignature) qualityScore += 0.2;
  else issues.push('Sem assinatura da Rafaela');

  if (hasGoodLength) qualityScore += 0.2;
  else issues.push('Tamanho inadequado');

  // Verificar tom adequado
  const hasGoodTone = response.toLowerCase().includes('rafaela') || 
                      response.includes('ajudar') || 
                      response.includes('cuidar') ||
                      response.includes('carinho');
  
  if (hasGoodTone) qualityScore += 0.1;
  else issues.push('Tom não adequado');

  return {
    usedRAG,
    isPersonalized,
    hasSignature,
    hasGoodLength,
    qualityScore: Math.min(1.0, qualityScore),
    issues
  };
}

function generateFinalReport(results: TestResult[]): void {
  console.log('📊 RELATÓRIO FINAL - INTEGRAÇÃO WHATSAPP + RAG\n');
  console.log('==============================================\n');

  const totalTests = results.length;
  const successfulTests = results.filter(r => r.qualityScore >= 0.7).length;
  const ragAccuracy = results.filter(r => r.usedRAG === r.expectedRAG).length;
  const avgQuality = results.reduce((sum, r) => sum + r.qualityScore, 0) / totalTests;
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / totalTests;

  console.log(`📈 MÉTRICAS GERAIS:`);
  console.log(`   - Total de testes: ${totalTests}`);
  console.log(`   - Testes bem-sucedidos: ${successfulTests}/${totalTests} (${(successfulTests/totalTests*100).toFixed(1)}%)`);
  console.log(`   - Precisão do RAG: ${ragAccuracy}/${totalTests} (${(ragAccuracy/totalTests*100).toFixed(1)}%)`);
  console.log(`   - Qualidade média: ${(avgQuality * 100).toFixed(1)}%`);
  console.log(`   - Tempo médio de resposta: ${avgResponseTime.toFixed(0)}ms`);

  console.log(`\n🎯 ANÁLISE POR CATEGORIA:`);
  
  const categories = ['Serviços municipais', 'Projetos de lei', 'Atendimento', 'Saudação', 'Programas sociais'];
  categories.forEach(category => {
    const categoryResults = results.filter(r => r.scenario.toLowerCase().includes(category.toLowerCase()));
    if (categoryResults.length > 0) {
      const categoryAvg = categoryResults.reduce((sum, r) => sum + r.qualityScore, 0) / categoryResults.length;
      console.log(`   - ${category}: ${(categoryAvg * 100).toFixed(1)}% (${categoryResults.length} testes)`);
    }
  });

  console.log(`\n⚠️ PROBLEMAS IDENTIFICADOS:`);
  const allIssues = results.flatMap(r => r.issues);
  const issueCount = allIssues.reduce((acc, issue) => {
    acc[issue] = (acc[issue] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  Object.entries(issueCount)
    .sort(([,a], [,b]) => b - a)
    .forEach(([issue, count]) => {
      console.log(`   - ${issue}: ${count} ocorrências`);
    });

  console.log(`\n🚀 RECOMENDAÇÕES:`);
  if (avgQuality >= 0.8) {
    console.log('   ✅ Sistema funcionando excelentemente!');
    console.log('   - Pronto para uso em produção');
    console.log('   - Considerar adicionar mais documentos à base de conhecimento');
  } else if (avgQuality >= 0.6) {
    console.log('   ⚠️ Sistema funcionando bem, mas com melhorias necessárias:');
    console.log('   - Ajustar prompts para melhor personalização');
    console.log('   - Verificar threshold do RAG');
    console.log('   - Adicionar mais exemplos de treinamento');
  } else {
    console.log('   ❌ Sistema precisa de ajustes significativos:');
    console.log('   - Revisar configuração do Pipeline RAG');
    console.log('   - Melhorar qualidade dos documentos');
    console.log('   - Ajustar prompts e validações');
  }

  console.log(`\n🎉 INTEGRAÇÃO WHATSAPP + RAG TESTADA COM SUCESSO!`);
}

// Executar teste se chamado diretamente
if (require.main === module) {
  (async () => {
    try {
      await testWhatsAppRAGIntegration();
      process.exit(0);
    } catch (error) {
      console.error('❌ Erro no teste de integração:', error);
      process.exit(1);
    }
  })();
}

export { testWhatsAppRAGIntegration };
