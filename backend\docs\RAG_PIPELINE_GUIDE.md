# Pipeline RAG Avançado - Guia de Uso

## Visão Geral

O Pipeline RAG (Retrieval-Augmented Generation) implementado é um sistema completo e avançado que segue as melhores práticas da indústria. Ele integra todos os componentes necessários para um sistema RAG de produção.

## Componentes Implementados

### 1. Ingestão & Pré-processamento (`documentIngestion.ts`)
- **Funcionalidade**: Captura documentos, planilhas, APIs e multimídia
- **Formatos Suportados**: PDF, DOCX, TXT, XLSX, CSV, JSON, XML, HTML
- **Recursos**:
  - Extração de texto inteligente
  - Limpeza e normalização de conteúdo
  - Suporte a múltiplas fontes (arquivos, URLs, APIs)
  - Processamento de metadados

### 2. Chunking Inteligente (`intelligentChunking.ts`)
- **Estratégias**: Semântica, hierárquica, deslizante, adaptativa
- **Recursos**:
  - Preservação de contexto com sobreposição
  - Análise de densidade de informação
  - Chunking baseado em embeddings
  - Métricas de qualidade dos chunks

### 3. Embeddings Multi-Modal (`embeddingService.ts` + `hybridEmbeddings.ts`)
- **Modelos**: Gemini text-embedding-004 + modelos contextuais
- **Recursos**:
  - Embeddings híbridos com múltiplos modelos
  - Combinação de conteúdo + metadados
  - Cache inteligente de embeddings

### 4. Armazenamento Vetorial (`advancedVectorStorage.ts`)
- **Tecnologias**: ChromaDB, FAISS, índices temporais
- **Recursos**:
  - Busca por similaridade, MMR, híbrida
  - Cache distribuído
  - Métricas de performance
  - Backup e restauração

### 5. Retrieval Híbrido (`hybridRetrieval.ts`)
- **Estratégias**: Vetorial, BM25, grafo, temporal, semântica, fuzzy
- **Fusão**: RRF, ponderada, aprendida, adaptativa
- **Recursos**:
  - Busca paralela em múltiplas estratégias
  - Diversificação de resultados (MMR)
  - Explicabilidade dos resultados

### 6. Reranking Inteligente (`rerankingService.ts`)
- **Métodos**: Cross-encoder, LLM scoring
- **Recursos**:
  - Filtragem de qualidade
  - Diversificação
  - Scoring contextual

### 7. Otimização de Contexto (`contextOptimization.ts`)
- **Técnicas**: Compressão semântica, sumarização, filtragem
- **Recursos**:
  - Ajuste automático para limites de tokens
  - Preservação de informações importantes
  - Múltiplas estratégias de compressão

### 8. Geração com LLM (`advancedRAGPipeline.ts`)
- **Modelo**: Gemini 1.5 Flash
- **Recursos**:
  - Prompt templates otimizados
  - Balanceamento de provedores
  - Geração de citações

### 9. Pós-processamento & Citações
- **Recursos**:
  - Referências inline automáticas
  - Validação de consistência
  - Detecção de alucinações

### 10. Feedback & Aprendizado Contínuo (`feedbackLearning.ts`)
- **Tipos**: Feedback explícito e implícito
- **Recursos**:
  - Ajuste automático de parâmetros
  - Melhoria contínua do sistema
  - Análise de padrões de uso

### 11. Monitoramento & Observabilidade (`ragMonitoring.ts`)
- **Métricas**: Latência, qualidade, custo, uso
- **Recursos**:
  - Alertas automáticos
  - Dashboards de métricas
  - Relatórios de saúde

## Como Usar

### 1. Configuração Inicial

```typescript
import { advancedRAGPipelineService } from './services/advancedRAGPipeline';

// Configurar o pipeline
const config = {
  ingestion: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    enableOCR: false,
    enableMultimedia: true
  },
  chunking: {
    strategy: 'semantic',
    chunkSize: 1000,
    overlap: 200
  },
  retrieval: {
    maxResults: 10,
    fusionMethod: 'rrf',
    enableHybridSearch: true
  },
  generation: {
    maxTokens: 8000,
    enableContextOptimization: true,
    enableCitations: true
  }
};

advancedRAGPipelineService.updateConfig(config);
```

### 2. Ingestão de Documentos

```typescript
// Ingerir documentos
const sources = [
  {
    type: 'file',
    source: '/path/to/document.pdf',
    metadata: { category: 'legislation', tags: ['municipal'] }
  },
  {
    type: 'url',
    source: 'https://example.com/api/data',
    metadata: { category: 'services' }
  }
];

const ingestionResult = await advancedRAGPipelineService.ingestDocuments(sources);
console.log(`Documentos processados: ${ingestionResult.summary.successful}`);
```

### 3. Processamento de Queries

```typescript
// Processar query
const query = {
  question: "Como solicitar alvará de funcionamento?",
  sessionId: "session_123",
  userId: "user_456",
  options: {
    maxResults: 5,
    includeExplanation: true,
    includeCitations: true
  }
};

const response = await advancedRAGPipelineService.processQuery(query);

console.log('Resposta:', response.answer);
console.log('Confiança:', response.confidence);
console.log('Fontes:', response.sources.length);
console.log('Tempo:', response.processingTime, 'ms');
```

### 4. Monitoramento

```typescript
import { ragMonitoringService } from './services/ragMonitoring';

// Obter métricas
const metrics = ragMonitoringService.getMetrics();
console.log('Performance:', metrics.performance);
console.log('Qualidade:', metrics.quality);

// Obter relatório de saúde
const healthReport = ragMonitoringService.generateHealthReport();
console.log('Status:', healthReport.status);
console.log('Score:', healthReport.score);
```

### 5. Feedback e Aprendizado

```typescript
// Processar feedback do usuário
await advancedRAGPipelineService.processFeedback('session_123', {
  rating: 4,
  helpful: true,
  comment: 'Resposta muito útil!'
});

// Otimizar pipeline automaticamente
const optimization = await advancedRAGPipelineService.optimizePipeline();
console.log('Otimizações aplicadas:', optimization.optimizations);
```

## Integração com Sistema Existente

### 1. Atualizar RAG Service Existente

```typescript
// Em ragService.ts, substituir implementação por:
import { advancedRAGPipelineService } from './advancedRAGPipeline';

export class RAGService {
  async query(question: string, options?: any): Promise<RAGResponse> {
    return advancedRAGPipelineService.processQuery({
      question,
      ...options
    });
  }
}
```

### 2. Integração com WhatsApp

```typescript
// Em whatsapp.ts, usar o pipeline avançado:
const ragResponse = await advancedRAGPipelineService.processQuery({
  question: message.body,
  sessionId: message.from,
  userId: contact.id
});

await client.sendText(message.from, ragResponse.answer);
```

## Configurações Avançadas

### 1. Chunking Personalizado

```typescript
import { intelligentChunkingService } from './services/intelligentChunking';

const chunkingResult = await intelligentChunkingService.createIntelligentChunks(
  content,
  {
    type: 'adaptive',
    chunkSize: 1500,
    overlap: 300,
    useEmbeddings: true,
    preserveBoundaries: true
  }
);
```

### 2. Retrieval Híbrido Customizado

```typescript
import { hybridRetrievalService } from './services/hybridRetrieval';

const searchResult = await hybridRetrievalService.search({
  text: query,
  searchStrategies: [
    { type: 'vector', weight: 0.4, enabled: true },
    { type: 'bm25', weight: 0.3, enabled: true },
    { type: 'semantic', weight: 0.2, enabled: true },
    { type: 'temporal', weight: 0.1, enabled: true }
  ],
  fusionMethod: 'adaptive',
  diversityFactor: 0.3
});
```

### 3. Otimização de Contexto

```typescript
import { contextOptimizationService } from './services/contextOptimization';

const optimizedContext = await contextOptimizationService.optimizeContext(
  chunks,
  query,
  {
    maxTokens: 6000,
    preserveImportant: true,
    useSemanticCompression: true
  }
);
```

## Métricas e Monitoramento

O sistema coleta automaticamente:

- **Performance**: Latência, throughput, taxa de erro
- **Qualidade**: Confiança, relevância, satisfação do usuário
- **Uso**: Queries totais, usuários únicos, queries populares
- **Recursos**: Memória, CPU, disco, custos de API

## Alertas Automáticos

O sistema gera alertas para:
- Latência alta (> 5 segundos)
- Taxa de erro alta (> 5%)
- Confiança baixa (< 60%)
- Uso de recursos alto (> 80%)

## Próximos Passos

1. **Teste o pipeline** com dados reais do gabinete
2. **Configure alertas** para monitoramento em produção
3. **Ajuste parâmetros** baseado nas métricas coletadas
4. **Implemente feedback** dos usuários para melhoria contínua
5. **Expanda a base de conhecimento** com mais documentos

O pipeline está pronto para produção e pode ser facilmente integrado ao sistema existente do gabinete da vereadora Rafaela.
