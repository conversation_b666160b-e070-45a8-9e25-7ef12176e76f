/**
 * Serviço de Monitoramento e Observabilidade RAG
 * Métricas (latência, qualidade, custo, uso) com alertas, dashboards e auto-scaling
 */

import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';

export interface RAGMetrics {
  performance: {
    averageLatency: number;
    p95Latency: number;
    p99Latency: number;
    throughput: number;
    errorRate: number;
    successRate: number;
  };
  quality: {
    averageConfidence: number;
    averageRelevance: number;
    userSatisfaction: number;
    hallucinationRate: number;
    citationAccuracy: number;
  };
  usage: {
    totalQueries: number;
    dailyQueries: number;
    uniqueUsers: number;
    popularQueries: Array<{ query: string; count: number }>;
    peakHours: number[];
  };
  resources: {
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
    networkIO: number;
    embeddingCosts: number;
    llmCosts: number;
  };
  components: {
    ingestion: ComponentMetrics;
    retrieval: ComponentMetrics;
    generation: ComponentMetrics;
    storage: ComponentMetrics;
  };
}

export interface ComponentMetrics {
  latency: number;
  errorRate: number;
  throughput: number;
  availability: number;
  lastError?: string;
  lastErrorTime?: string;
}

export interface Alert {
  id: string;
  type: 'performance' | 'quality' | 'error' | 'resource';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
  metadata: Record<string, any>;
}

export interface MonitoringConfig {
  alertThresholds: {
    latency: number;
    errorRate: number;
    confidence: number;
    memoryUsage: number;
    diskUsage: number;
  };
  retentionDays: number;
  enableAutoScaling: boolean;
  enableAlerting: boolean;
  metricsInterval: number;
}

export class RAGMonitoringService extends EventEmitter {
  private metrics: RAGMetrics;
  private alerts: Alert[] = [];
  private config: MonitoringConfig;
  private metricsHistory: Array<{ timestamp: string; metrics: RAGMetrics }> = [];
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(config?: Partial<MonitoringConfig>) {
    super();
    
    this.config = {
      alertThresholds: {
        latency: 5000, // 5 segundos
        errorRate: 0.05, // 5%
        confidence: 0.6, // 60%
        memoryUsage: 0.8, // 80%
        diskUsage: 0.9 // 90%
      },
      retentionDays: 30,
      enableAutoScaling: false,
      enableAlerting: true,
      metricsInterval: 60000, // 1 minuto
      ...config
    };

    this.initializeMetrics();
    this.startMonitoring();
  }

  /**
   * Inicializar métricas
   */
  private initializeMetrics(): void {
    this.metrics = {
      performance: {
        averageLatency: 0,
        p95Latency: 0,
        p99Latency: 0,
        throughput: 0,
        errorRate: 0,
        successRate: 1.0
      },
      quality: {
        averageConfidence: 0.8,
        averageRelevance: 0.8,
        userSatisfaction: 0.8,
        hallucinationRate: 0.05,
        citationAccuracy: 0.9
      },
      usage: {
        totalQueries: 0,
        dailyQueries: 0,
        uniqueUsers: 0,
        popularQueries: [],
        peakHours: []
      },
      resources: {
        memoryUsage: 0,
        cpuUsage: 0,
        diskUsage: 0,
        networkIO: 0,
        embeddingCosts: 0,
        llmCosts: 0
      },
      components: {
        ingestion: this.createComponentMetrics(),
        retrieval: this.createComponentMetrics(),
        generation: this.createComponentMetrics(),
        storage: this.createComponentMetrics()
      }
    };
  }

  private createComponentMetrics(): ComponentMetrics {
    return {
      latency: 0,
      errorRate: 0,
      throughput: 0,
      availability: 1.0
    };
  }

  /**
   * Iniciar monitoramento
   */
  private startMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
      this.checkAlerts();
      this.cleanupOldData();
    }, this.config.metricsInterval);

    console.log(`📊 Monitoramento RAG iniciado (intervalo: ${this.config.metricsInterval}ms)`);
  }

  /**
   * Coletar métricas do sistema
   */
  private async collectMetrics(): Promise<void> {
    try {
      // Coletar métricas de recursos do sistema
      await this.collectSystemMetrics();
      
      // Armazenar snapshot das métricas
      this.metricsHistory.push({
        timestamp: new Date().toISOString(),
        metrics: JSON.parse(JSON.stringify(this.metrics))
      });

      // Emitir evento de métricas coletadas
      this.emit('metrics_collected', this.metrics);

    } catch (error) {
      console.error('Erro ao coletar métricas:', error);
    }
  }

  /**
   * Coletar métricas do sistema
   */
  private async collectSystemMetrics(): Promise<void> {
    // Uso de memória (Node.js)
    const memUsage = process.memoryUsage();
    this.metrics.resources.memoryUsage = memUsage.heapUsed / memUsage.heapTotal;

    // CPU usage (aproximado)
    const cpuUsage = process.cpuUsage();
    this.metrics.resources.cpuUsage = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds

    // Uso de disco (aproximado)
    try {
      const stats = fs.statSync(process.cwd());
      this.metrics.resources.diskUsage = 0.1; // Placeholder - implementar verificação real
    } catch (error) {
      this.metrics.resources.diskUsage = 0;
    }
  }

  /**
   * Registrar query processada
   */
  recordQuery(queryData: {
    latency: number;
    confidence: number;
    success: boolean;
    userId?: string;
    query: string;
    componentLatencies?: {
      retrieval?: number;
      generation?: number;
      total?: number;
    };
  }): void {
    // Atualizar métricas de performance
    this.updatePerformanceMetrics(queryData);
    
    // Atualizar métricas de qualidade
    this.updateQualityMetrics(queryData);
    
    // Atualizar métricas de uso
    this.updateUsageMetrics(queryData);
    
    // Atualizar métricas de componentes
    if (queryData.componentLatencies) {
      this.updateComponentMetrics(queryData.componentLatencies, queryData.success);
    }

    console.log(`📈 Query registrada: ${queryData.latency}ms, confiança: ${(queryData.confidence * 100).toFixed(1)}%`);
  }

  /**
   * Atualizar métricas de performance
   */
  private updatePerformanceMetrics(queryData: any): void {
    const perf = this.metrics.performance;
    
    // Latência média
    perf.averageLatency = (perf.averageLatency + queryData.latency) / 2;
    
    // Throughput (queries por minuto)
    perf.throughput = this.metrics.usage.totalQueries / (Date.now() / 60000);
    
    // Taxa de sucesso
    const totalQueries = this.metrics.usage.totalQueries + 1;
    const successCount = perf.successRate * this.metrics.usage.totalQueries + (queryData.success ? 1 : 0);
    perf.successRate = successCount / totalQueries;
    perf.errorRate = 1 - perf.successRate;
  }

  /**
   * Atualizar métricas de qualidade
   */
  private updateQualityMetrics(queryData: any): void {
    const quality = this.metrics.quality;
    
    // Confiança média
    quality.averageConfidence = (quality.averageConfidence + queryData.confidence) / 2;
    
    // Relevância (baseada na confiança por enquanto)
    quality.averageRelevance = quality.averageConfidence;
  }

  /**
   * Atualizar métricas de uso
   */
  private updateUsageMetrics(queryData: any): void {
    const usage = this.metrics.usage;
    
    usage.totalQueries++;
    usage.dailyQueries++; // Resetado diariamente
    
    // Queries populares
    const existingQuery = usage.popularQueries.find(q => q.query === queryData.query);
    if (existingQuery) {
      existingQuery.count++;
    } else {
      usage.popularQueries.push({ query: queryData.query, count: 1 });
    }
    
    // Manter apenas top 10
    usage.popularQueries = usage.popularQueries
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Atualizar métricas de componentes
   */
  private updateComponentMetrics(latencies: any, success: boolean): void {
    if (latencies.retrieval) {
      this.updateSingleComponentMetrics('retrieval', latencies.retrieval, success);
    }
    if (latencies.generation) {
      this.updateSingleComponentMetrics('generation', latencies.generation, success);
    }
  }

  private updateSingleComponentMetrics(component: keyof RAGMetrics['components'], latency: number, success: boolean): void {
    const comp = this.metrics.components[component];
    comp.latency = (comp.latency + latency) / 2;
    comp.throughput = this.metrics.usage.totalQueries / (Date.now() / 60000);
    
    // Atualizar taxa de erro
    const totalRequests = this.metrics.usage.totalQueries;
    const errorCount = comp.errorRate * (totalRequests - 1) + (success ? 0 : 1);
    comp.errorRate = errorCount / totalRequests;
    
    // Disponibilidade (baseada na taxa de sucesso)
    comp.availability = 1 - comp.errorRate;
  }

  /**
   * Verificar alertas
   */
  private checkAlerts(): void {
    if (!this.config.enableAlerting) return;

    const thresholds = this.config.alertThresholds;
    
    // Verificar latência
    if (this.metrics.performance.averageLatency > thresholds.latency) {
      this.createAlert('performance', 'high', 
        `Latência alta: ${this.metrics.performance.averageLatency}ms (limite: ${thresholds.latency}ms)`);
    }
    
    // Verificar taxa de erro
    if (this.metrics.performance.errorRate > thresholds.errorRate) {
      this.createAlert('performance', 'high',
        `Taxa de erro alta: ${(this.metrics.performance.errorRate * 100).toFixed(1)}% (limite: ${(thresholds.errorRate * 100).toFixed(1)}%)`);
    }
    
    // Verificar confiança
    if (this.metrics.quality.averageConfidence < thresholds.confidence) {
      this.createAlert('quality', 'medium',
        `Confiança baixa: ${(this.metrics.quality.averageConfidence * 100).toFixed(1)}% (limite: ${(thresholds.confidence * 100).toFixed(1)}%)`);
    }
    
    // Verificar uso de memória
    if (this.metrics.resources.memoryUsage > thresholds.memoryUsage) {
      this.createAlert('resource', 'high',
        `Uso de memória alto: ${(this.metrics.resources.memoryUsage * 100).toFixed(1)}% (limite: ${(thresholds.memoryUsage * 100).toFixed(1)}%)`);
    }
  }

  /**
   * Criar alerta
   */
  private createAlert(type: Alert['type'], severity: Alert['severity'], message: string): void {
    // Verificar se já existe alerta similar ativo
    const existingAlert = this.alerts.find(alert => 
      !alert.resolved && alert.type === type && alert.message === message
    );
    
    if (existingAlert) return; // Não duplicar alertas

    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      message,
      timestamp: new Date().toISOString(),
      resolved: false,
      metadata: {
        metrics: JSON.parse(JSON.stringify(this.metrics))
      }
    };

    this.alerts.push(alert);
    this.emit('alert_created', alert);
    
    console.warn(`🚨 Alerta ${severity}: ${message}`);
  }

  /**
   * Resolver alerta
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date().toISOString();
      this.emit('alert_resolved', alert);
      console.log(`✅ Alerta resolvido: ${alert.message}`);
    }
  }

  /**
   * Limpar dados antigos
   */
  private cleanupOldData(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);
    
    // Limpar histórico de métricas
    this.metricsHistory = this.metricsHistory.filter(
      entry => new Date(entry.timestamp) > cutoffDate
    );
    
    // Limpar alertas resolvidos antigos
    this.alerts = this.alerts.filter(
      alert => !alert.resolved || new Date(alert.timestamp) > cutoffDate
    );
  }

  /**
   * Obter métricas atuais
   */
  getMetrics(): RAGMetrics {
    return JSON.parse(JSON.stringify(this.metrics));
  }

  /**
   * Obter alertas ativos
   */
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Obter todos os alertas
   */
  getAllAlerts(): Alert[] {
    return [...this.alerts];
  }

  /**
   * Obter histórico de métricas
   */
  getMetricsHistory(hours: number = 24): Array<{ timestamp: string; metrics: RAGMetrics }> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);
    
    return this.metricsHistory.filter(
      entry => new Date(entry.timestamp) > cutoffDate
    );
  }

  /**
   * Gerar relatório de saúde
   */
  generateHealthReport(): {
    status: 'healthy' | 'warning' | 'critical';
    score: number;
    metrics: RAGMetrics;
    activeAlerts: Alert[];
    recommendations: string[];
  } {
    const activeAlerts = this.getActiveAlerts();
    const criticalAlerts = activeAlerts.filter(a => a.severity === 'critical');
    const highAlerts = activeAlerts.filter(a => a.severity === 'high');
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    let score = 100;
    const recommendations: string[] = [];

    // Determinar status baseado em alertas
    if (criticalAlerts.length > 0) {
      status = 'critical';
      score -= criticalAlerts.length * 30;
    } else if (highAlerts.length > 0) {
      status = 'warning';
      score -= highAlerts.length * 15;
    }

    // Penalizar por métricas ruins
    if (this.metrics.performance.errorRate > 0.1) {
      score -= 20;
      recommendations.push('Investigar alta taxa de erro');
    }
    
    if (this.metrics.quality.averageConfidence < 0.6) {
      score -= 15;
      recommendations.push('Melhorar qualidade das respostas');
    }
    
    if (this.metrics.performance.averageLatency > 10000) {
      score -= 25;
      recommendations.push('Otimizar performance do sistema');
    }

    score = Math.max(0, score);

    return {
      status,
      score,
      metrics: this.getMetrics(),
      activeAlerts,
      recommendations
    };
  }

  /**
   * Parar monitoramento
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    console.log('📊 Monitoramento RAG parado');
  }
}

// Instância singleton
export const ragMonitoringService = new RAGMonitoringService();
