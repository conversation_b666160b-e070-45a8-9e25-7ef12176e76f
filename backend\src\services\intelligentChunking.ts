/**
 * Serviço de Chunking Inteligente
 * Segmenta textos via estratégias semântica, hierárquica e deslizante com sobreposição
 */

import { v4 as uuidv4 } from 'uuid';
import { embeddingService } from './embeddingService';

export interface ChunkingStrategy {
  type: 'semantic' | 'hierarchical' | 'sliding' | 'adaptive' | 'sentence' | 'paragraph';
  chunkSize: number;
  overlap: number;
  minChunkSize?: number;
  maxChunkSize?: number;
  preserveBoundaries?: boolean;
  useEmbeddings?: boolean;
}

export interface IntelligentChunk {
  id: string;
  content: string;
  index: number;
  startPosition: number;
  endPosition: number;
  metadata: {
    chunkType: string;
    size: number;
    wordCount: number;
    sentenceCount: number;
    paragraphCount: number;
    semanticScore?: number;
    coherenceScore?: number;
    boundaryType?: string;
    parentChunkId?: string;
    childChunkIds?: string[];
    [key: string]: any;
  };
  embedding?: number[];
}

export interface ChunkingResult {
  chunks: IntelligentChunk[];
  totalChunks: number;
  averageChunkSize: number;
  strategy: string;
  processingTime: number;
  qualityMetrics: {
    coherenceScore: number;
    coverageScore: number;
    overlapEfficiency: number;
  };
}

export class IntelligentChunkingService {
  private sentenceBoundaryRegex = /[.!?]+\s+/g;
  private paragraphBoundaryRegex = /\n\s*\n/g;
  private sectionBoundaryRegex = /\n\s*#{1,6}\s+/g;

  /**
   * Chunking principal com estratégia inteligente
   */
  async createIntelligentChunks(
    content: string,
    strategy: ChunkingStrategy,
    documentMetadata?: Record<string, any>
  ): Promise<ChunkingResult> {
    const startTime = Date.now();
    
    console.log(`🧩 Iniciando chunking inteligente: ${strategy.type} (tamanho: ${strategy.chunkSize}, overlap: ${strategy.overlap})`);

    let chunks: IntelligentChunk[];

    switch (strategy.type) {
      case 'semantic':
        chunks = await this.createSemanticChunks(content, strategy);
        break;
      case 'hierarchical':
        chunks = await this.createHierarchicalChunks(content, strategy);
        break;
      case 'sliding':
        chunks = this.createSlidingChunks(content, strategy);
        break;
      case 'adaptive':
        chunks = await this.createAdaptiveChunks(content, strategy);
        break;
      case 'sentence':
        chunks = this.createSentenceBasedChunks(content, strategy);
        break;
      case 'paragraph':
        chunks = this.createParagraphBasedChunks(content, strategy);
        break;
      default:
        chunks = this.createSlidingChunks(content, strategy);
    }

    // Calcular métricas de qualidade
    const qualityMetrics = await this.calculateQualityMetrics(chunks, content);
    
    const processingTime = Date.now() - startTime;
    const averageChunkSize = chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / chunks.length;

    console.log(`✅ Chunking concluído: ${chunks.length} chunks em ${processingTime}ms (média: ${Math.round(averageChunkSize)} chars)`);

    return {
      chunks,
      totalChunks: chunks.length,
      averageChunkSize,
      strategy: strategy.type,
      processingTime,
      qualityMetrics
    };
  }

  /**
   * Chunking semântico baseado em similaridade
   */
  private async createSemanticChunks(content: string, strategy: ChunkingStrategy): Promise<IntelligentChunk[]> {
    const sentences = this.splitIntoSentences(content);
    const chunks: IntelligentChunk[] = [];
    
    let currentChunk = '';
    let currentSentences: string[] = [];
    let startPosition = 0;

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i];
      const potentialChunk = currentChunk + (currentChunk ? ' ' : '') + sentence;

      // Verificar se excede o tamanho máximo
      if (potentialChunk.length > strategy.chunkSize && currentChunk.length > 0) {
        // Calcular score semântico se habilitado
        let semanticScore = 0;
        if (strategy.useEmbeddings && currentSentences.length > 1) {
          semanticScore = await this.calculateSemanticCoherence(currentSentences);
        }

        // Criar chunk
        const chunk = this.createChunk(
          currentChunk,
          chunks.length,
          startPosition,
          startPosition + currentChunk.length,
          {
            chunkType: 'semantic',
            sentenceCount: currentSentences.length,
            semanticScore
          }
        );

        chunks.push(chunk);

        // Iniciar novo chunk com overlap
        const overlapSentences = this.getOverlapSentences(currentSentences, strategy.overlap);
        currentSentences = [...overlapSentences, sentence];
        currentChunk = currentSentences.join(' ');
        startPosition = content.indexOf(currentChunk);
      } else {
        currentSentences.push(sentence);
        currentChunk = potentialChunk;
        if (chunks.length === 0) {
          startPosition = content.indexOf(sentence);
        }
      }
    }

    // Adicionar último chunk
    if (currentChunk.trim().length > 0) {
      const semanticScore = strategy.useEmbeddings && currentSentences.length > 1 
        ? await this.calculateSemanticCoherence(currentSentences) 
        : 0;

      chunks.push(this.createChunk(
        currentChunk,
        chunks.length,
        startPosition,
        startPosition + currentChunk.length,
        {
          chunkType: 'semantic',
          sentenceCount: currentSentences.length,
          semanticScore
        }
      ));
    }

    return chunks;
  }

  /**
   * Chunking hierárquico baseado na estrutura do documento
   */
  private async createHierarchicalChunks(content: string, strategy: ChunkingStrategy): Promise<IntelligentChunk[]> {
    const chunks: IntelligentChunk[] = [];
    
    // Detectar estrutura hierárquica (títulos, seções, parágrafos)
    const sections = this.detectDocumentStructure(content);
    
    for (const section of sections) {
      if (section.content.length <= strategy.chunkSize) {
        // Seção cabe em um chunk
        chunks.push(this.createChunk(
          section.content,
          chunks.length,
          section.startPosition,
          section.endPosition,
          {
            chunkType: 'hierarchical',
            level: section.level,
            title: section.title,
            boundaryType: 'section'
          }
        ));
      } else {
        // Seção precisa ser dividida
        const subChunks = await this.createSemanticChunks(section.content, {
          ...strategy,
          type: 'semantic'
        });
        
        // Adicionar metadados hierárquicos
        subChunks.forEach((chunk, index) => {
          chunk.metadata.parentSection = section.title;
          chunk.metadata.sectionLevel = section.level;
          chunk.metadata.chunkType = 'hierarchical-sub';
          chunks.push(chunk);
        });
      }
    }

    return chunks;
  }

  /**
   * Chunking deslizante (sliding window)
   */
  private createSlidingChunks(content: string, strategy: ChunkingStrategy): IntelligentChunk[] {
    const chunks: IntelligentChunk[] = [];
    const step = strategy.chunkSize - strategy.overlap;
    
    for (let i = 0; i < content.length; i += step) {
      const end = Math.min(i + strategy.chunkSize, content.length);
      const chunkContent = content.substring(i, end);
      
      if (chunkContent.trim().length > (strategy.minChunkSize || 50)) {
        // Ajustar para preservar limites de palavras se habilitado
        let adjustedContent = chunkContent;
        let adjustedEnd = end;
        
        if (strategy.preserveBoundaries && end < content.length) {
          const lastSpaceIndex = chunkContent.lastIndexOf(' ');
          if (lastSpaceIndex > chunkContent.length * 0.8) {
            adjustedContent = chunkContent.substring(0, lastSpaceIndex);
            adjustedEnd = i + lastSpaceIndex;
          }
        }

        chunks.push(this.createChunk(
          adjustedContent.trim(),
          chunks.length,
          i,
          adjustedEnd,
          {
            chunkType: 'sliding',
            boundaryType: strategy.preserveBoundaries ? 'word' : 'character'
          }
        ));
      }
    }
    
    return chunks;
  }

  /**
   * Chunking adaptativo baseado no conteúdo
   */
  private async createAdaptiveChunks(content: string, strategy: ChunkingStrategy): Promise<IntelligentChunk[]> {
    // Analisar densidade de informação do conteúdo
    const contentAnalysis = this.analyzeContentDensity(content);
    
    // Ajustar estratégia baseada na análise
    const adaptedStrategy: ChunkingStrategy = {
      ...strategy,
      chunkSize: this.adaptChunkSize(strategy.chunkSize, contentAnalysis),
      overlap: this.adaptOverlap(strategy.overlap, contentAnalysis)
    };

    // Usar estratégia semântica com parâmetros adaptados
    return this.createSemanticChunks(content, adaptedStrategy);
  }

  /**
   * Chunking baseado em sentenças
   */
  private createSentenceBasedChunks(content: string, strategy: ChunkingStrategy): IntelligentChunk[] {
    const sentences = this.splitIntoSentences(content);
    const chunks: IntelligentChunk[] = [];
    
    let currentChunk = '';
    let currentSentences: string[] = [];
    let startPosition = 0;

    for (const sentence of sentences) {
      const potentialChunk = currentChunk + (currentChunk ? ' ' : '') + sentence;
      
      if (potentialChunk.length > strategy.chunkSize && currentChunk.length > 0) {
        chunks.push(this.createChunk(
          currentChunk,
          chunks.length,
          startPosition,
          startPosition + currentChunk.length,
          {
            chunkType: 'sentence',
            sentenceCount: currentSentences.length,
            boundaryType: 'sentence'
          }
        ));

        // Overlap baseado em sentenças
        const overlapSentences = this.getOverlapSentences(currentSentences, strategy.overlap);
        currentSentences = [...overlapSentences, sentence];
        currentChunk = currentSentences.join(' ');
        startPosition = content.indexOf(currentChunk);
      } else {
        currentSentences.push(sentence);
        currentChunk = potentialChunk;
        if (chunks.length === 0) {
          startPosition = content.indexOf(sentence);
        }
      }
    }

    // Último chunk
    if (currentChunk.trim().length > 0) {
      chunks.push(this.createChunk(
        currentChunk,
        chunks.length,
        startPosition,
        startPosition + currentChunk.length,
        {
          chunkType: 'sentence',
          sentenceCount: currentSentences.length,
          boundaryType: 'sentence'
        }
      ));
    }

    return chunks;
  }

  /**
   * Chunking baseado em parágrafos
   */
  private createParagraphBasedChunks(content: string, strategy: ChunkingStrategy): IntelligentChunk[] {
    const paragraphs = content.split(this.paragraphBoundaryRegex).filter(p => p.trim().length > 0);
    const chunks: IntelligentChunk[] = [];
    
    let currentChunk = '';
    let currentParagraphs: string[] = [];
    let startPosition = 0;

    for (const paragraph of paragraphs) {
      const potentialChunk = currentChunk + (currentChunk ? '\n\n' : '') + paragraph;
      
      if (potentialChunk.length > strategy.chunkSize && currentChunk.length > 0) {
        chunks.push(this.createChunk(
          currentChunk,
          chunks.length,
          startPosition,
          startPosition + currentChunk.length,
          {
            chunkType: 'paragraph',
            paragraphCount: currentParagraphs.length,
            boundaryType: 'paragraph'
          }
        ));

        // Overlap baseado em parágrafos
        const overlapParagraphs = this.getOverlapParagraphs(currentParagraphs, strategy.overlap);
        currentParagraphs = [...overlapParagraphs, paragraph];
        currentChunk = currentParagraphs.join('\n\n');
        startPosition = content.indexOf(currentChunk);
      } else {
        currentParagraphs.push(paragraph);
        currentChunk = potentialChunk;
        if (chunks.length === 0) {
          startPosition = content.indexOf(paragraph);
        }
      }
    }

    // Último chunk
    if (currentChunk.trim().length > 0) {
      chunks.push(this.createChunk(
        currentChunk,
        chunks.length,
        startPosition,
        startPosition + currentChunk.length,
        {
          chunkType: 'paragraph',
          paragraphCount: currentParagraphs.length,
          boundaryType: 'paragraph'
        }
      ));
    }

    return chunks;
  }

  /**
   * Criar chunk com metadados
   */
  private createChunk(
    content: string,
    index: number,
    startPosition: number,
    endPosition: number,
    additionalMetadata: Record<string, any> = {}
  ): IntelligentChunk {
    const wordCount = content.split(/\s+/).filter(w => w.length > 0).length;
    const sentenceCount = content.split(this.sentenceBoundaryRegex).filter(s => s.trim().length > 0).length;
    const paragraphCount = content.split(this.paragraphBoundaryRegex).filter(p => p.trim().length > 0).length;

    return {
      id: uuidv4(),
      content: content.trim(),
      index,
      startPosition,
      endPosition,
      metadata: {
        size: content.length,
        wordCount,
        sentenceCount,
        paragraphCount,
        ...additionalMetadata
      }
    };
  }

  /**
   * Dividir texto em sentenças
   */
  private splitIntoSentences(text: string): string[] {
    return text
      .split(this.sentenceBoundaryRegex)
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }

  /**
   * Obter sentenças de overlap
   */
  private getOverlapSentences(sentences: string[], overlapSize: number): string[] {
    if (overlapSize <= 0 || sentences.length === 0) return [];

    // Calcular quantas sentenças representam o overlap
    const totalChars = sentences.join(' ').length;
    const overlapChars = Math.min(overlapSize, totalChars * 0.5);

    let currentChars = 0;
    const overlapSentences: string[] = [];

    for (let i = sentences.length - 1; i >= 0; i--) {
      const sentence = sentences[i];
      if (currentChars + sentence.length <= overlapChars) {
        overlapSentences.unshift(sentence);
        currentChars += sentence.length;
      } else {
        break;
      }
    }

    return overlapSentences;
  }

  /**
   * Obter parágrafos de overlap
   */
  private getOverlapParagraphs(paragraphs: string[], overlapSize: number): string[] {
    if (overlapSize <= 0 || paragraphs.length === 0) return [];

    const totalChars = paragraphs.join('\n\n').length;
    const overlapChars = Math.min(overlapSize, totalChars * 0.5);

    let currentChars = 0;
    const overlapParagraphs: string[] = [];

    for (let i = paragraphs.length - 1; i >= 0; i--) {
      const paragraph = paragraphs[i];
      if (currentChars + paragraph.length <= overlapChars) {
        overlapParagraphs.unshift(paragraph);
        currentChars += paragraph.length;
      } else {
        break;
      }
    }

    return overlapParagraphs;
  }

  /**
   * Calcular coerência semântica entre sentenças
   */
  private async calculateSemanticCoherence(sentences: string[]): Promise<number> {
    if (sentences.length < 2) return 1.0;

    try {
      // Gerar embeddings para cada sentença
      const embeddings = await Promise.all(
        sentences.map(sentence => embeddingService.generateEmbedding(sentence))
      );

      // Calcular similaridade média entre sentenças adjacentes
      let totalSimilarity = 0;
      let comparisons = 0;

      for (let i = 0; i < embeddings.length - 1; i++) {
        const similarity = this.cosineSimilarity(
          embeddings[i].embedding,
          embeddings[i + 1].embedding
        );
        totalSimilarity += similarity;
        comparisons++;
      }

      return comparisons > 0 ? totalSimilarity / comparisons : 0;
    } catch (error) {
      console.warn('Erro ao calcular coerência semântica:', error);
      return 0;
    }
  }

  /**
   * Calcular similaridade cosseno entre dois vetores
   */
  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }

  /**
   * Detectar estrutura hierárquica do documento
   */
  private detectDocumentStructure(content: string): Array<{
    title: string;
    content: string;
    level: number;
    startPosition: number;
    endPosition: number;
  }> {
    const sections: Array<{
      title: string;
      content: string;
      level: number;
      startPosition: number;
      endPosition: number;
    }> = [];

    // Detectar títulos markdown
    const lines = content.split('\n');
    let currentSection = {
      title: 'Início',
      content: '',
      level: 0,
      startPosition: 0,
      endPosition: 0
    };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);

      if (headerMatch) {
        // Finalizar seção anterior
        if (currentSection.content.trim().length > 0) {
          currentSection.endPosition = content.indexOf(line);
          sections.push({ ...currentSection });
        }

        // Iniciar nova seção
        currentSection = {
          title: headerMatch[2],
          content: '',
          level: headerMatch[1].length,
          startPosition: content.indexOf(line),
          endPosition: 0
        };
      } else {
        currentSection.content += line + '\n';
      }
    }

    // Adicionar última seção
    if (currentSection.content.trim().length > 0) {
      currentSection.endPosition = content.length;
      sections.push(currentSection);
    }

    // Se não encontrou estrutura, criar uma seção única
    if (sections.length === 0) {
      sections.push({
        title: 'Documento',
        content: content,
        level: 1,
        startPosition: 0,
        endPosition: content.length
      });
    }

    return sections;
  }

  /**
   * Analisar densidade de informação do conteúdo
   */
  private analyzeContentDensity(content: string): {
    density: 'low' | 'medium' | 'high';
    complexity: number;
    technicalTerms: number;
    averageSentenceLength: number;
  } {
    const sentences = this.splitIntoSentences(content);
    const words = content.split(/\s+/).filter(w => w.length > 0);

    const averageSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;

    // Detectar termos técnicos (palavras longas, maiúsculas, números)
    const technicalTerms = words.filter(word =>
      word.length > 8 ||
      /[A-Z]{2,}/.test(word) ||
      /\d+/.test(word)
    ).length;

    const technicalRatio = technicalTerms / words.length;

    let density: 'low' | 'medium' | 'high' = 'medium';
    if (technicalRatio > 0.3 || averageSentenceLength > 20) {
      density = 'high';
    } else if (technicalRatio < 0.1 && averageSentenceLength < 12) {
      density = 'low';
    }

    return {
      density,
      complexity: technicalRatio,
      technicalTerms,
      averageSentenceLength
    };
  }

  /**
   * Adaptar tamanho do chunk baseado na análise de conteúdo
   */
  private adaptChunkSize(baseSize: number, analysis: ReturnType<typeof this.analyzeContentDensity>): number {
    switch (analysis.density) {
      case 'high':
        return Math.round(baseSize * 0.8); // Chunks menores para conteúdo denso
      case 'low':
        return Math.round(baseSize * 1.2); // Chunks maiores para conteúdo simples
      default:
        return baseSize;
    }
  }

  /**
   * Adaptar overlap baseado na análise de conteúdo
   */
  private adaptOverlap(baseOverlap: number, analysis: ReturnType<typeof this.analyzeContentDensity>): number {
    switch (analysis.density) {
      case 'high':
        return Math.round(baseOverlap * 1.3); // Mais overlap para conteúdo complexo
      case 'low':
        return Math.round(baseOverlap * 0.8); // Menos overlap para conteúdo simples
      default:
        return baseOverlap;
    }
  }

  /**
   * Calcular métricas de qualidade dos chunks
   */
  private async calculateQualityMetrics(chunks: IntelligentChunk[], originalContent: string): Promise<{
    coherenceScore: number;
    coverageScore: number;
    overlapEfficiency: number;
  }> {
    // Calcular cobertura (quanto do conteúdo original está coberto)
    const totalOriginalLength = originalContent.length;
    const totalChunkLength = chunks.reduce((sum, chunk) => sum + chunk.content.length, 0);
    const coverageScore = Math.min(totalChunkLength / totalOriginalLength, 1.0);

    // Calcular eficiência do overlap
    const totalOverlap = totalChunkLength - totalOriginalLength;
    const expectedOverlap = chunks.length > 1 ? (chunks.length - 1) * 200 : 0; // Assumindo 200 chars de overlap
    const overlapEfficiency = expectedOverlap > 0 ? Math.min(totalOverlap / expectedOverlap, 1.0) : 1.0;

    // Calcular coerência média (se disponível)
    const coherenceScores = chunks
      .map(chunk => chunk.metadata.semanticScore || chunk.metadata.coherenceScore)
      .filter(score => typeof score === 'number');

    const coherenceScore = coherenceScores.length > 0
      ? coherenceScores.reduce((sum, score) => sum + score, 0) / coherenceScores.length
      : 0.8; // Score padrão se não calculado

    return {
      coherenceScore,
      coverageScore,
      overlapEfficiency
    };
  }
}

// Instância singleton
export const intelligentChunkingService = new IntelligentChunkingService();
