#!/bin/bash

# Teste de Integração WhatsApp + Pipeline RAG Avançado
# Simula mensagens do WhatsApp para testar a qualidade das respostas

echo "🧪 TESTE DE INTEGRAÇÃO WHATSAPP + PIPELINE RAG AVANÇADO"
echo "======================================================="
echo ""

# URL base do servidor
BASE_URL="http://localhost:3002"

# Função para testar uma query RAG
test_rag_query() {
    local question="$1"
    local description="$2"
    
    echo "📱 Teste: $description"
    echo "💬 Pergunta: \"$question\""
    echo ""
    
    # Fazer requisição para o endpoint RAG
    response=$(curl -s -X POST "$BASE_URL/api/knowledge-base/rag/query" \
        -H "Content-Type: application/json" \
        -d "{
            \"question\": \"$question\",
            \"sessionId\": \"whatsapp_test_$(date +%s)\",
            \"maxResults\": 3,
            \"threshold\": 0.4
        }" \
        --max-time 30)
    
    # Verificar se a resposta foi bem-sucedida
    if echo "$response" | grep -q '"success":true'; then
        # Extrair dados da resposta
        answer=$(echo "$response" | jq -r '.response.answer // "Erro ao extrair resposta"')
        confidence=$(echo "$response" | jq -r '.response.confidence // 0')
        sources=$(echo "$response" | jq -r '.response.sources | length // 0')
        processing_time=$(echo "$response" | jq -r '.response.processingTime // 0')
        
        echo "✅ Resposta obtida:"
        echo "   \"$answer\""
        echo ""
        echo "📊 Métricas:"
        echo "   - Confiança: $(echo "$confidence * 100" | bc -l | cut -d. -f1)%"
        echo "   - Fontes encontradas: $sources"
        echo "   - Tempo de processamento: ${processing_time}ms"
        echo "   - Tamanho da resposta: ${#answer} caracteres"
        
        # Análise da qualidade
        echo ""
        echo "🔍 Análise da qualidade:"
        
        # Verificar se tem informações específicas
        if echo "$answer" | grep -qE "(R\$|dias|horário|telefone|endereço|projeto|programa)"; then
            echo "   ✅ Contém informações específicas (RAG ativo)"
        else
            echo "   ❌ Resposta genérica (RAG não utilizado)"
        fi
        
        # Verificar personalização
        if echo "$answer" | grep -qi "rafaela"; then
            echo "   ✅ Resposta personalizada com identidade"
        else
            echo "   ⚠️ Sem personalização clara"
        fi
        
        # Verificar assinatura
        if echo "$answer" | grep -q "🧡"; then
            echo "   ✅ Tem assinatura da Rafaela"
        else
            echo "   ❌ Sem assinatura"
        fi
        
        # Verificar tamanho adequado
        if [ ${#answer} -ge 20 ] && [ ${#answer} -le 300 ]; then
            echo "   ✅ Tamanho adequado"
        else
            echo "   ⚠️ Tamanho inadequado"
        fi
        
    else
        echo "❌ Erro na requisição:"
        echo "$response" | jq -r '.error // "Erro desconhecido"'
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# Primeiro, verificar se o servidor está rodando
echo "🔍 Verificando se o servidor está ativo..."
if curl -s "$BASE_URL/api/knowledge-base/rag/metrics" > /dev/null; then
    echo "✅ Servidor ativo e respondendo"
    echo ""
else
    echo "❌ Servidor não está respondendo. Certifique-se de que está rodando na porta 3002"
    exit 1
fi

# Testes de cenários realistas do WhatsApp
echo "🚀 Iniciando testes de cenários WhatsApp..."
echo ""

# Teste 1: Serviços municipais
test_rag_query "Como faço para solicitar alvará de funcionamento?" "Pergunta sobre serviços municipais"

# Teste 2: Projetos de lei
test_rag_query "Quais são os projetos da vereadora para gestantes?" "Pergunta sobre projetos de lei"

# Teste 3: Atendimento
test_rag_query "Qual o horário de atendimento do gabinete?" "Pergunta sobre atendimento"

# Teste 4: Programa específico
test_rag_query "Como funciona o auxílio maternidade?" "Pergunta sobre programa específico"

# Teste 5: Serviços de saúde
test_rag_query "Onde fica a UBS mais próxima?" "Pergunta sobre serviços de saúde"

# Teste 6: Programa social
test_rag_query "Como participar do programa mulher empreendedora?" "Pergunta sobre programa social"

# Teste 7: Informações de contato
test_rag_query "Qual o telefone do gabinete da vereadora?" "Pergunta sobre contatos"

# Teste 8: Saudação (deve ter resposta mais genérica)
test_rag_query "Oi, bom dia! Como você pode me ajudar?" "Saudação geral"

# Verificar métricas finais do sistema
echo "📊 MÉTRICAS FINAIS DO SISTEMA"
echo "============================="
echo ""

metrics_response=$(curl -s "$BASE_URL/api/knowledge-base/rag/metrics" --max-time 15)

if echo "$metrics_response" | grep -q '"success":true'; then
    total_queries=$(echo "$metrics_response" | jq -r '.metrics.pipeline.totalQueries // 0')
    avg_response_time=$(echo "$metrics_response" | jq -r '.metrics.pipeline.averageResponseTime // 0')
    success_rate=$(echo "$metrics_response" | jq -r '.metrics.monitoring.performance.successRate // 0')
    avg_confidence=$(echo "$metrics_response" | jq -r '.metrics.monitoring.quality.averageConfidence // 0')
    
    echo "📈 Estatísticas do Pipeline RAG:"
    echo "   - Total de queries processadas: $total_queries"
    echo "   - Tempo médio de resposta: ${avg_response_time}ms"
    echo "   - Taxa de sucesso: $(echo "$success_rate * 100" | bc -l | cut -d. -f1)%"
    echo "   - Confiança média: $(echo "$avg_confidence * 100" | bc -l | cut -d. -f1)%"
    
    # Verificar alertas ativos
    active_alerts=$(echo "$metrics_response" | jq -r '.health.activeAlerts | length // 0')
    echo "   - Alertas ativos: $active_alerts"
    
    if [ "$active_alerts" -gt 0 ]; then
        echo ""
        echo "🚨 Alertas ativos:"
        echo "$metrics_response" | jq -r '.health.activeAlerts[].message // "Sem detalhes"' | sed 's/^/   - /'
    fi
    
else
    echo "❌ Erro ao obter métricas do sistema"
fi

echo ""
echo "🎉 TESTE DE INTEGRAÇÃO WHATSAPP + RAG CONCLUÍDO!"
echo ""
echo "🚀 PRÓXIMOS PASSOS:"
echo "   1. Conectar WhatsApp real usando QR Code"
echo "   2. Testar com usuários reais"
echo "   3. Monitorar qualidade das respostas"
echo "   4. Ajustar base de conhecimento conforme necessário"
echo ""
echo "📱 Para conectar o WhatsApp:"
echo "   - Acesse o frontend em http://localhost:3001"
echo "   - Vá para a aba 'WhatsApp e IA'"
echo "   - Clique em 'Conectar ao WhatsApp'"
echo "   - Escaneie o QR Code com seu WhatsApp"
echo ""
