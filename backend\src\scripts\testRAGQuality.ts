/**
 * Script de Teste de Qualidade do Pipeline RAG
 * Testa respostas com documentos de exemplo e avalia qualidade
 */

import { ragService } from '../services/ragService';
import { ragMonitoringService } from '../services/ragMonitoring';
import { documentIngestionService } from '../services/documentIngestion';
import * as fs from 'fs';
import * as path from 'path';

interface QualityTest {
  question: string;
  expectedTopics: string[];
  category?: string;
  expectedConfidence: number;
  description: string;
}

const qualityTests: QualityTest[] = [
  {
    question: "Como solicitar alvará de funcionamento?",
    expectedTopics: ["documentos", "taxa", "prazo", "secretaria"],
    category: "servicos",
    expectedConfidence: 0.8,
    description: "Teste básico sobre procedimentos administrativos"
  },
  {
    question: "Qual o valor da taxa para alvará de funcionamento?",
    expectedTopics: ["R$ 150,00", "taxa municipal"],
    category: "servicos", 
    expectedConfidence: 0.9,
    description: "Teste específico sobre valores"
  },
  {
    question: "Quais são os projetos de lei da vereadora sobre maternidade?",
    expectedTopics: ["Programa Municipal de Apoio à Maternidade", "auxílio financeiro", "R$ 300,00"],
    category: "legislacao",
    expectedConfidence: 0.8,
    description: "Teste sobre projetos legislativos específicos"
  },
  {
    question: "Como funciona o Programa Mulher Empreendedora?",
    expectedTopics: ["microcrédito", "R$ 5.000,00", "cursos gratuitos", "feira mensal"],
    category: "legislacao",
    expectedConfidence: 0.8,
    description: "Teste sobre programas sociais"
  },
  {
    question: "Qual o horário de atendimento da vereadora?",
    expectedTopics: ["segunda a sexta", "8h às 12h", "14h às 18h"],
    category: "agenda",
    expectedConfidence: 0.9,
    description: "Teste sobre informações de atendimento"
  },
  {
    question: "Quais são os telefones das UBS?",
    expectedTopics: ["(84) 3333-1111", "(84) 3333-2222", "(84) 3333-3333"],
    category: "servicos",
    expectedConfidence: 0.9,
    description: "Teste sobre informações de contato"
  },
  {
    question: "Como funciona o transporte público na cidade?",
    expectedTopics: ["5 linhas", "R$ 3,50", "gratuidade", "idosos"],
    category: "servicos",
    expectedConfidence: 0.8,
    description: "Teste sobre serviços públicos"
  },
  {
    question: "Quais eventos estão programados para dezembro?",
    expectedTopics: ["Natal Solidário", "Caminhada pela Paz", "Casa de Passagem"],
    category: "agenda",
    expectedConfidence: 0.7,
    description: "Teste sobre agenda e eventos"
  },
  {
    question: "Como denunciar violência doméstica?",
    expectedTopics: ["Central de atendimento 24h", "Casa de Passagem"],
    category: "legislacao",
    expectedConfidence: 0.7,
    description: "Teste sobre temas sensíveis"
  },
  {
    question: "Quais são os critérios para receber auxílio maternidade?",
    expectedTopics: ["renda familiar", "2 salários mínimos", "CadÚnico", "pré-natal"],
    category: "legislacao",
    expectedConfidence: 0.8,
    description: "Teste sobre critérios específicos"
  }
];

async function ingestTestDocuments(): Promise<void> {
  console.log('📥 Ingerindo documentos de teste...');
  
  const testDocsDir = path.join(process.cwd(), 'test-documents');
  
  if (!fs.existsSync(testDocsDir)) {
    console.log('❌ Diretório test-documents não encontrado');
    return;
  }

  const sources = [
    {
      type: 'file' as const,
      source: path.join(testDocsDir, 'servicos_municipais.txt'),
      metadata: {
        category: 'servicos',
        documentType: 'manual',
        title: 'Serviços Municipais',
        tags: ['alvará', 'licença', 'saúde', 'educação', 'transporte']
      }
    },
    {
      type: 'file' as const,
      source: path.join(testDocsDir, 'projetos_lei.txt'),
      metadata: {
        category: 'legislacao',
        documentType: 'projeto_lei',
        title: 'Projetos de Lei da Vereadora Rafaela',
        tags: ['maternidade', 'primeira_infancia', 'empreendedorismo', 'violencia_domestica']
      }
    },
    {
      type: 'file' as const,
      source: path.join(testDocsDir, 'agenda_eventos.txt'),
      metadata: {
        category: 'agenda',
        documentType: 'cronograma',
        title: 'Agenda e Eventos',
        tags: ['reuniões', 'eventos', 'atendimento', 'projetos']
      }
    }
  ];

  try {
    const result = await ragService.ingestDocuments(sources);
    console.log(`✅ Documentos ingeridos: ${result.successful}/${sources.length}`);
    console.log(`📊 Chunks criados: ${result.chunksCreated}`);
  } catch (error) {
    console.error('❌ Erro na ingestão:', error);
  }
}

async function runQualityTests(): Promise<void> {
  console.log('\n🧪 Iniciando testes de qualidade do RAG...\n');

  const results = {
    totalTests: qualityTests.length,
    passed: 0,
    failed: 0,
    averageConfidence: 0,
    averageResponseTime: 0,
    topicCoverage: 0,
    details: [] as any[]
  };

  for (let i = 0; i < qualityTests.length; i++) {
    const test = qualityTests[i];
    console.log(`📝 Teste ${i + 1}/${qualityTests.length}: ${test.description}`);
    console.log(`❓ Pergunta: "${test.question}"`);

    try {
      const startTime = Date.now();
      
      const response = await ragService.processQuery({
        question: test.question,
        category: test.category,
        sessionId: `quality_test_${i + 1}`,
        maxResults: 5
      });

      const responseTime = Date.now() - startTime;
      
      // Avaliar qualidade da resposta
      const quality = evaluateResponseQuality(response, test);
      
      console.log(`✅ Resposta (${responseTime}ms):`);
      console.log(`   ${response.answer.substring(0, 150)}...`);
      console.log(`📊 Métricas:`);
      console.log(`   - Confiança: ${(response.confidence * 100).toFixed(1)}%`);
      console.log(`   - Fontes encontradas: ${response.sources.length}`);
      console.log(`   - Tópicos cobertos: ${quality.topicsCovered}/${test.expectedTopics.length}`);
      console.log(`   - Score de qualidade: ${(quality.qualityScore * 100).toFixed(1)}%`);
      
      if (quality.qualityScore >= 0.7) {
        console.log(`✅ PASSOU no teste`);
        results.passed++;
      } else {
        console.log(`❌ FALHOU no teste`);
        results.failed++;
      }

      results.averageConfidence += response.confidence;
      results.averageResponseTime += responseTime;
      results.topicCoverage += quality.topicsCovered / test.expectedTopics.length;

      results.details.push({
        test: test.description,
        question: test.question,
        confidence: response.confidence,
        responseTime,
        qualityScore: quality.qualityScore,
        topicsCovered: quality.topicsCovered,
        expectedTopics: test.expectedTopics.length,
        passed: quality.qualityScore >= 0.7
      });

      console.log('');

    } catch (error) {
      console.error(`❌ Erro no teste: ${error}`);
      results.failed++;
      console.log('');
    }
  }

  // Calcular médias
  results.averageConfidence /= qualityTests.length;
  results.averageResponseTime /= qualityTests.length;
  results.topicCoverage /= qualityTests.length;

  // Exibir resumo final
  console.log('📊 RESUMO DOS TESTES DE QUALIDADE');
  console.log('=====================================');
  console.log(`Total de testes: ${results.totalTests}`);
  console.log(`Testes aprovados: ${results.passed} (${(results.passed/results.totalTests*100).toFixed(1)}%)`);
  console.log(`Testes reprovados: ${results.failed} (${(results.failed/results.totalTests*100).toFixed(1)}%)`);
  console.log(`Confiança média: ${(results.averageConfidence * 100).toFixed(1)}%`);
  console.log(`Tempo médio de resposta: ${results.averageResponseTime.toFixed(0)}ms`);
  console.log(`Cobertura de tópicos: ${(results.topicCoverage * 100).toFixed(1)}%`);
  console.log('');

  // Classificar qualidade geral
  const overallScore = (results.passed / results.totalTests + results.averageConfidence + results.topicCoverage) / 3;
  
  if (overallScore >= 0.8) {
    console.log('🎉 QUALIDADE EXCELENTE - Sistema RAG funcionando perfeitamente!');
  } else if (overallScore >= 0.6) {
    console.log('✅ QUALIDADE BOA - Sistema RAG funcionando bem com algumas melhorias possíveis');
  } else if (overallScore >= 0.4) {
    console.log('⚠️ QUALIDADE REGULAR - Sistema RAG precisa de ajustes');
  } else {
    console.log('❌ QUALIDADE BAIXA - Sistema RAG precisa de revisão significativa');
  }

  console.log(`Score geral: ${(overallScore * 100).toFixed(1)}%`);
  
  // Obter métricas do sistema
  console.log('\n📈 MÉTRICAS DO SISTEMA:');
  const systemMetrics = ragService.getMetrics();
  console.log('Pipeline:', systemMetrics.pipeline);
  console.log('Monitoramento:', systemMetrics.monitoring?.performance);
  
  return;
}

function evaluateResponseQuality(response: any, test: QualityTest): {
  qualityScore: number;
  topicsCovered: number;
  issues: string[];
} {
  const issues: string[] = [];
  let qualityScore = 0;
  let topicsCovered = 0;

  // 1. Verificar confiança
  if (response.confidence >= test.expectedConfidence) {
    qualityScore += 0.3;
  } else {
    issues.push(`Confiança baixa: ${(response.confidence * 100).toFixed(1)}% (esperado: ${(test.expectedConfidence * 100).toFixed(1)}%)`);
  }

  // 2. Verificar se encontrou fontes
  if (response.sources.length > 0) {
    qualityScore += 0.2;
  } else {
    issues.push('Nenhuma fonte encontrada');
  }

  // 3. Verificar cobertura de tópicos esperados
  const answerLower = response.answer.toLowerCase();
  for (const topic of test.expectedTopics) {
    if (answerLower.includes(topic.toLowerCase())) {
      topicsCovered++;
    }
  }

  const topicCoverage = topicsCovered / test.expectedTopics.length;
  qualityScore += topicCoverage * 0.4;

  // 4. Verificar tamanho da resposta
  if (response.answer.length > 50 && response.answer.length < 1000) {
    qualityScore += 0.1;
  } else if (response.answer.length <= 50) {
    issues.push('Resposta muito curta');
  } else {
    issues.push('Resposta muito longa');
  }

  return {
    qualityScore: Math.min(1.0, qualityScore),
    topicsCovered,
    issues
  };
}

// Executar testes se chamado diretamente
if (require.main === module) {
  (async () => {
    try {
      await ingestTestDocuments();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Aguardar processamento
      await runQualityTests();
      process.exit(0);
    } catch (error) {
      console.error('❌ Erro nos testes:', error);
      process.exit(1);
    }
  })();
}

export { runQualityTests, ingestTestDocuments };
