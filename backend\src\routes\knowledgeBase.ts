/**
 * <PERSON><PERSON><PERSON> da API para Gestão da Base de Conhecimento
 * Upload, processamento e busca de documentos
 */

import express from 'express';
import multer from 'multer';
import path from 'path';
import { documentProcessor, DocumentInfo } from '../services/documentProcessor';
import { simpleVectorDatabase, DocumentMetadata } from '../services/simpleVectorDB';
import { ragService } from '../services/ragService';
import { intentClassifier } from '../services/intentClassifier';
import { semanticCache } from '../services/semanticCache';
import { feedbackLearningService } from '../services/feedbackLearning';
import { incrementalIndexingService } from '../services/incrementalIndexing';
import { hybridEmbeddingService } from '../services/hybridEmbeddings';
import { chromaVectorDB } from '../services/chromaVectorDB';
import { simpleVectorDatabase } from '../services/simpleVectorDB';

const router = express.Router();

// Configuração do multer para upload de arquivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'documents');
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `doc-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 10 // Máximo 10 arquivos por vez
  },
  fileFilter: (req, file, cb) => {
    if (documentProcessor.isSupportedFileType(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Tipo de arquivo não suportado: ${file.mimetype}`));
    }
  }
});

/**
 * POST /api/knowledge-base/upload
 * Upload e processamento de documentos
 */
router.post('/upload', upload.array('documents', 10), async (req, res) => {
  try {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Nenhum arquivo foi enviado'
      });
    }

    // Extrair metadados do corpo da requisição
    const {
      documentType = 'admin',
      category = 'other',
      source = 'upload',
      tags = []
    } = req.body;

    console.log(`📤 Upload recebido: ${files.length} arquivo(s)`);

    // Converter arquivos para formato esperado
    const documentInfos: DocumentInfo[] = files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      path: file.path
    }));

    // Validar arquivos
    const validationErrors: string[] = [];
    for (const docInfo of documentInfos) {
      const validation = documentProcessor.validateFile(docInfo);
      if (!validation.valid) {
        validationErrors.push(`${docInfo.originalName}: ${validation.error}`);
      }
    }

    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Arquivos inválidos',
        details: validationErrors
      });
    }

    // Processar documentos
    const baseMetadata = {
      documentType: documentType as DocumentMetadata['documentType'],
      category: category as DocumentMetadata['category'],
      source,
      tags: Array.isArray(tags) ? tags : tags.split(',').map((t: string) => t.trim())
    };

    const results = await documentProcessor.processMultipleDocuments(
      documentInfos,
      baseMetadata
    );

    // Compilar resposta
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    res.json({
      success: true,
      message: `${successful.length}/${results.length} documentos processados com sucesso`,
      results: {
        successful: successful.length,
        failed: failed.length,
        details: results
      }
    });

  } catch (error) {
    console.error('❌ Erro no upload:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/knowledge-base/search
 * Busca semântica na base de conhecimento
 */
router.get('/search', async (req, res) => {
  try {
    const {
      q: query,
      limit = 10,
      threshold = 0.7,
      category,
      documentType,
      includeMetadata = true
    } = req.query;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Parâmetro de busca "q" é obrigatório'
      });
    }

    console.log(`🔍 Busca: "${query}"`);

    // Construir filtros
    const filters: Record<string, any> = {};
    if (category) filters.category = category;
    if (documentType) filters.documentType = documentType;

    // Realizar busca
    const results = await simpleVectorDatabase.search(query, {
      limit: parseInt(limit as string),
      threshold: parseFloat(threshold as string),
      filter: filters,
      includeMetadata: includeMetadata === 'true'
    });

    res.json({
      success: true,
      query,
      results: results.length,
      data: results
    });

  } catch (error) {
    console.error('❌ Erro na busca:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/knowledge-base/document/:id
 * Obter documento específico
 */
router.get('/document/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const document = await simpleVectorDatabase.getDocument(id);
    
    if (!document) {
      return res.status(404).json({
        success: false,
        error: 'Documento não encontrado'
      });
    }

    res.json({
      success: true,
      document
    });

  } catch (error) {
    console.error('❌ Erro ao obter documento:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter documento'
    });
  }
});

/**
 * DELETE /api/knowledge-base/document/:id
 * Remover documento
 */
router.delete('/document/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const removed = await documentProcessor.removeDocument(id);
    
    if (removed) {
      res.json({
        success: true,
        message: 'Documento removido com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Documento não encontrado'
      });
    }

  } catch (error) {
    console.error('❌ Erro ao remover documento:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao remover documento'
    });
  }
});

/**
 * GET /api/knowledge-base/stats
 * Estatísticas da base de conhecimento
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await simpleVectorDatabase.getStats();
    
    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('❌ Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas'
    });
  }
});

/**
 * GET /api/knowledge-base/supported-types
 * Tipos de arquivo suportados
 */
router.get('/supported-types', (req, res) => {
  try {
    const supportedTypes = documentProcessor.getSupportedTypes();
    
    res.json({
      success: true,
      supportedTypes
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao obter tipos suportados'
    });
  }
});

/**
 * POST /api/knowledge-base/search-by-category
 * Busca por categoria específica
 */
router.post('/search-by-category', async (req, res) => {
  try {
    const { query, category, limit = 5 } = req.body;

    if (!query || !category) {
      return res.status(400).json({
        success: false,
        error: 'Query e category são obrigatórios'
      });
    }

    const results = await simpleVectorDatabase.searchByCategory(
      query,
      category,
      parseInt(limit)
    );

    res.json({
      success: true,
      query,
      category,
      results: results.length,
      data: results
    });

  } catch (error) {
    console.error('❌ Erro na busca por categoria:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca por categoria'
    });
  }
});

/**
 * POST /api/knowledge-base/initialize
 * Inicializar/reinicializar a base de conhecimento
 */
router.post('/initialize', async (req, res) => {
  try {
    await simpleVectorDatabase.initialize();
    
    res.json({
      success: true,
      message: 'Base de conhecimento inicializada com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao inicializar:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao inicializar base de conhecimento'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/query
 * Consulta RAG completa com contexto usando pipeline avançado
 */
router.post('/rag/query', async (req, res) => {
  try {
    const {
      question,
      context,
      category,
      documentType,
      maxResults = 5,
      threshold = 0.7,
      sessionId,
      userId
    } = req.body;

    if (!question || typeof question !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    console.log(`🚀 Consulta RAG Avançada: "${question}"`);

    const response = await ragService.processQuery({
      question,
      context,
      category,
      documentType,
      maxResults: parseInt(maxResults),
      threshold: parseFloat(threshold),
      sessionId: sessionId || `session_${Date.now()}`,
      userId
    });

    // Log das métricas do pipeline avançado
    if (response.pipeline) {
      console.log(`📊 Pipeline Metrics:`, {
        retrieval: `${response.pipeline.retrievalTime}ms`,
        reranking: `${response.pipeline.rerankingTime}ms`,
        contextOptimization: `${response.pipeline.contextOptimizationTime}ms`,
        generation: `${response.pipeline.generationTime}ms`,
        total: `${response.processingTime}ms`
      });
    }

    res.json({
      success: true,
      query: question,
      response,
      // Informações adicionais do pipeline avançado
      advanced: {
        pipelineUsed: true,
        explanation: response.explanation,
        citations: response.citations,
        qualityScore: response.metadata.qualityScore,
        usedStrategies: response.metadata.usedStrategies
      }
    });

  } catch (error) {
    console.error('❌ Erro na consulta RAG:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na consulta RAG',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/advanced-test
 * Teste específico do pipeline RAG avançado
 */
router.post('/rag/advanced-test', async (req, res) => {
  try {
    const { question = "Como solicitar alvará de funcionamento?" } = req.body;

    console.log(`🧪 Teste Pipeline RAG Avançado: "${question}"`);

    // Obter métricas antes
    const metricsBefore = ragService.getMetrics();

    // Processar query
    const response = await ragService.processQuery({
      question,
      sessionId: `test_${Date.now()}`,
      maxResults: 5,
      threshold: 0.5
    });

    // Obter métricas depois
    const metricsAfter = ragService.getMetrics();

    // Obter relatório de saúde
    const healthReport = ragService.generateHealthReport();

    res.json({
      success: true,
      test: {
        query: question,
        response,
        metrics: {
          before: metricsBefore,
          after: metricsAfter
        },
        health: healthReport,
        pipelineComponents: {
          ingestion: '✅ Implementado',
          chunking: '✅ Implementado',
          vectorStorage: '✅ Implementado',
          hybridRetrieval: '✅ Implementado',
          reranking: '✅ Implementado',
          contextOptimization: '✅ Implementado',
          generation: '✅ Implementado',
          monitoring: '✅ Implementado',
          feedbackLearning: '✅ Implementado'
        }
      }
    });

  } catch (error) {
    console.error('❌ Erro no teste do pipeline avançado:', error);
    res.status(500).json({
      success: false,
      error: 'Erro no teste do pipeline',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/ingest-documents
 * Ingerir documentos usando o pipeline avançado
 */
router.post('/rag/ingest-documents', async (req, res) => {
  try {
    const { sources } = req.body;

    if (!sources || !Array.isArray(sources)) {
      return res.status(400).json({
        success: false,
        error: 'Campo "sources" deve ser um array de fontes'
      });
    }

    console.log(`📥 Ingestão avançada: ${sources.length} fontes`);

    const result = await ragService.ingestDocuments(sources);

    res.json({
      success: true,
      ingestion: result,
      message: `${result.successful} documentos processados com sucesso`
    });

  } catch (error) {
    console.error('❌ Erro na ingestão:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na ingestão de documentos',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/feedback
 * Processar feedback do usuário
 */
router.post('/rag/feedback', async (req, res) => {
  try {
    const { sessionId, rating, helpful, comment, corrections } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Campo "sessionId" é obrigatório'
      });
    }

    await ragService.processFeedback(sessionId, {
      rating: rating || 3,
      helpful: helpful !== false,
      comment,
      corrections
    });

    res.json({
      success: true,
      message: 'Feedback processado com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao processar feedback:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao processar feedback',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/knowledge-base/rag/metrics
 * Obter métricas detalhadas do pipeline
 */
router.get('/rag/metrics', async (req, res) => {
  try {
    const metrics = ragService.getMetrics();
    const healthReport = ragService.generateHealthReport();
    const config = ragService.getConfig();

    res.json({
      success: true,
      metrics,
      health: healthReport,
      config,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao obter métricas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter métricas',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/legislation
 * Busca específica sobre legislação
 */
router.post('/rag/legislation', async (req, res) => {
  try {
    const { question } = req.body;

    if (!question) {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    const response = await ragService.searchLegislation(question);

    res.json({
      success: true,
      query: question,
      type: 'legislation',
      response
    });

  } catch (error) {
    console.error('❌ Erro na busca de legislação:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca de legislação'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/services
 * Busca específica sobre serviços
 */
router.post('/rag/services', async (req, res) => {
  try {
    const { question } = req.body;

    if (!question) {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    const response = await ragService.searchServices(question);

    res.json({
      success: true,
      query: question,
      type: 'services',
      response
    });

  } catch (error) {
    console.error('❌ Erro na busca de serviços:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca de serviços'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/faq
 * Busca específica no FAQ
 */
router.post('/rag/faq', async (req, res) => {
  try {
    const { question } = req.body;

    if (!question) {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    const response = await ragService.searchFAQ(question);

    res.json({
      success: true,
      query: question,
      type: 'faq',
      response
    });

  } catch (error) {
    console.error('❌ Erro na busca FAQ:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca FAQ'
    });
  }
});

/**
 * POST /api/knowledge-base/classify-intent
 * Classificar intenção de uma consulta
 */
router.post('/classify-intent', async (req, res) => {
  try {
    const {
      query,
      threshold = 0.7,
      includeReasoning = true,
      suggestActions = true
    } = req.body;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Campo "query" é obrigatório'
      });
    }

    console.log(`🎯 Classificando intenção: "${query}"`);

    const classification = await intentClassifier.classifyIntent(query, {
      threshold: parseFloat(threshold),
      includeReasoning: includeReasoning === true,
      suggestActions: suggestActions === true
    });

    res.json({
      success: true,
      query,
      classification
    });

  } catch (error) {
    console.error('❌ Erro na classificação de intenção:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na classificação de intenção',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/knowledge-base/intents
 * Obter estatísticas e lista de intenções disponíveis
 */
router.get('/intents', async (req, res) => {
  try {
    const statistics = intentClassifier.getIntentStatistics();

    res.json({
      success: true,
      statistics
    });

  } catch (error) {
    console.error('❌ Erro ao obter estatísticas de intenções:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas de intenções'
    });
  }
});

/**
 * POST /api/knowledge-base/smart-query
 * Consulta inteligente: classifica intenção + busca RAG
 */
router.post('/smart-query', async (req, res) => {
  try {
    const {
      question,
      classifyFirst = true,
      maxResults = 5,
      threshold = 0.7
    } = req.body;

    if (!question || typeof question !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    console.log(`🧠 Consulta inteligente: "${question}"`);

    let classification = null;

    // 1. Classificar intenção se solicitado
    if (classifyFirst) {
      classification = await intentClassifier.classifyIntent(question, {
        threshold: parseFloat(threshold),
        includeReasoning: true,
        suggestActions: true
      });
    }

    // 2. Buscar na base de conhecimento
    const ragResponse = await ragService.processQuery({
      question,
      category: classification?.intent.category,
      maxResults: parseInt(maxResults),
      threshold: parseFloat(threshold)
    });

    // 3. Combinar resultados
    res.json({
      success: true,
      query: question,
      classification,
      ragResponse,
      smartSuggestions: {
        useClassification: classification && classification.confidence > 0.8,
        requiresHuman: classification?.requiresHuman || ragResponse.confidence < 0.5,
        recommendedActions: classification?.suggestedActions || []
      }
    });

  } catch (error) {
    console.error('❌ Erro na consulta inteligente:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na consulta inteligente',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/knowledge-base/cache/stats
 * Estatísticas do cache semântico
 */
router.get('/cache/stats', async (req, res) => {
  try {
    const stats = semanticCache.getStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas do cache:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas do cache'
    });
  }
});

/**
 * DELETE /api/knowledge-base/cache/clear
 * Limpar cache semântico
 */
router.delete('/cache/clear', async (req, res) => {
  try {
    semanticCache.clear();

    res.json({
      success: true,
      message: 'Cache semântico limpo com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao limpar cache:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao limpar cache'
    });
  }
});

/**
 * POST /api/knowledge-base/feedback
 * Registrar feedback do usuário
 */
router.post('/feedback', async (req, res) => {
  try {
    const feedback = req.body;

    await feedbackLearningService.recordFeedback(feedback);

    res.json({
      success: true,
      message: 'Feedback registrado com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao registrar feedback:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao registrar feedback'
    });
  }
});

/**
 * GET /api/knowledge-base/performance
 * Obter insights de performance
 */
router.get('/performance', async (req, res) => {
  try {
    const insights = await feedbackLearningService.getPerformanceInsights();

    res.json({
      success: true,
      insights
    });
  } catch (error) {
    console.error('❌ Erro ao obter insights:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter insights de performance'
    });
  }
});

/**
 * POST /api/knowledge-base/train
 * Treinar modelo baseado em feedback
 */
router.post('/train', async (req, res) => {
  try {
    await feedbackLearningService.trainFromFeedback();

    res.json({
      success: true,
      message: 'Treinamento concluído com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro no treinamento:', error);
    res.status(500).json({
      success: false,
      error: 'Erro no treinamento do modelo'
    });
  }
});

/**
 * POST /api/knowledge-base/index/incremental
 * Executar indexação incremental
 */
router.post('/index/incremental', async (req, res) => {
  try {
    const job = await incrementalIndexingService.runIncrementalIndexing();

    res.json({
      success: true,
      job
    });
  } catch (error) {
    console.error('❌ Erro na indexação incremental:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na indexação incremental'
    });
  }
});

/**
 * GET /api/knowledge-base/index/stats
 * Obter estatísticas de indexação
 */
router.get('/index/stats', async (req, res) => {
  try {
    const stats = await incrementalIndexingService.getIndexingStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas de indexação'
    });
  }
});

/**
 * GET /api/knowledge-base/embeddings/stats
 * Obter estatísticas dos embeddings híbridos
 */
router.get('/embeddings/stats', async (req, res) => {
  try {
    const stats = hybridEmbeddingService.getModelStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas dos embeddings:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas dos embeddings'
    });
  }
});

/**
 * GET /api/knowledge-base/chroma/stats
 * Obter estatísticas do ChromaDB
 */
router.get('/chroma/stats', async (req, res) => {
  try {
    const stats = await chromaVectorDB.getStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas do ChromaDB:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas do ChromaDB'
    });
  }
});

/**
 * DELETE /api/knowledge-base/documents/clear
 * Limpar todos os documentos indexados
 */
router.delete('/documents/clear', async (req, res) => {
  try {
    console.log('🧹 Iniciando limpeza de todos os documentos...');

    // Obter estatísticas antes da limpeza
    const statsBefore = await simpleVectorDatabase.getStats();
    const documentsToRemove = statsBefore.totalDocuments;

    console.log(`📊 Documentos a serem removidos: ${documentsToRemove}`);

    // Limpar ChromaDB se disponível
    try {
      await chromaVectorDB.clear();
      console.log('✅ ChromaDB limpo');
    } catch (error) {
      console.warn('⚠️ ChromaDB não disponível ou erro na limpeza:', error);
    }

    // Limpar sistema simples
    await simpleVectorDatabase.clear();
    console.log('✅ Sistema de vetores simples limpo');

    // Limpar cache semântico
    semanticCache.clear();
    console.log('✅ Cache semântico limpo');

    // Verificar se realmente foi limpo
    const statsAfter = await simpleVectorDatabase.getStats();

    res.json({
      success: true,
      message: 'Todos os documentos foram removidos com sucesso',
      documentsRemoved: documentsToRemove,
      documentsRemaining: statsAfter.totalDocuments,
      clearedSystems: ['simpleVectorDB', 'semanticCache', 'chromaDB']
    });

    console.log(`🎉 Limpeza concluída: ${documentsToRemove} documentos removidos`);

  } catch (error) {
    console.error('❌ Erro ao limpar documentos:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao limpar documentos: ' + error.message
    });
  }
});

/**
 * DELETE /api/knowledge-base/documents/reset
 * Reset completo do sistema (documentos + cache + configurações)
 */
router.delete('/documents/reset', async (req, res) => {
  try {
    console.log('🔄 Iniciando reset completo do sistema...');

    // Limpar todos os documentos
    await simpleVectorDatabase.clear();

    // Limpar ChromaDB
    try {
      await chromaVectorDB.clear();
    } catch (error) {
      console.warn('⚠️ ChromaDB não disponível');
    }

    // Limpar cache
    semanticCache.clear();

    // Limpar histórico de indexação
    try {
      const fs = require('fs');
      const path = require('path');
      const historyFile = path.join(process.cwd(), 'data', 'indexing_history.json');
      if (fs.existsSync(historyFile)) {
        fs.unlinkSync(historyFile);
        console.log('✅ Histórico de indexação limpo');
      }
    } catch (error) {
      console.warn('⚠️ Erro ao limpar histórico:', error);
    }

    res.json({
      success: true,
      message: 'Reset completo realizado com sucesso',
      clearedItems: [
        'Documentos indexados',
        'Cache semântico',
        'ChromaDB',
        'Histórico de indexação'
      ]
    });

    console.log('🎉 Reset completo concluído');

  } catch (error) {
    console.error('❌ Erro no reset:', error);
    res.status(500).json({
      success: false,
      error: 'Erro no reset: ' + error.message
    });
  }
});

export default router;
