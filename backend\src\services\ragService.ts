/**
 * Serviço RAG (Retrieval-Augmented Generation) - Pipeline Avançado
 * Integra sistema RAG completo com todos os componentes avançados
 */

import {
  advancedRAGPipelineService,
  RAGQuery as AdvancedRAGQuery,
  RAGResponse as AdvancedRAGResponse
} from './advancedRAGPipeline';
import { ragMonitoringService } from './ragMonitoring';
import { documentIngestionService, IngestionSource } from './documentIngestion';

// Manter compatibilidade com interfaces existentes
export interface RAGQuery {
  question: string;
  context?: string;
  category?: string;
  documentType?: string;
  maxResults?: number;
  threshold?: number;
  useHybridEmbeddings?: boolean;
  useChromaDB?: boolean;
  sessionId?: string;
  userId?: string;
}

export interface RAGResponse {
  answer: string;
  sources: any[];
  confidence: number;
  processingTime: number;
  usedContext: boolean;
  metadata: {
    queryType: string;
    documentsFound: number;
    averageSimilarity: number;
    geminiModel: string;
    // Novos campos do pipeline avançado
    chunksProcessed?: number;
    tokensUsed?: number;
    compressionRatio?: number;
    qualityScore?: number;
    usedStrategies?: string[];
  };
  // Novos campos opcionais
  explanation?: string[];
  citations?: Array<{
    source: string;
    content: string;
    relevance: number;
  }>;
  pipeline?: {
    retrievalTime: number;
    rerankingTime: number;
    contextOptimizationTime: number;
    generationTime: number;
  };
}

export interface ContextualPrompt {
  systemPrompt: string;
  userPrompt: string;
  context: string;
  sources: string[];
}

// Manter interface SearchResult para compatibilidade
export interface SearchResult {
  id: string;
  content: string;
  metadata: any;
  similarity: number;
  distance: number;
}

export class RAGService {
  constructor() {
    console.log('🚀 RAG Service inicializado com pipeline avançado');
  }

  /**
   * Processar consulta RAG completa usando pipeline avançado
   */
  async processQuery(query: RAGQuery): Promise<RAGResponse> {
    try {
      console.log(`🔍 Processando consulta RAG: "${query.question}"`);

      // Converter query para formato do pipeline avançado
      const advancedQuery: AdvancedRAGQuery = {
        question: query.question,
        context: query.context,
        sessionId: query.sessionId,
        userId: query.userId,
        filters: this.buildFilters(query),
        options: {
          maxResults: query.maxResults || 5,
          includeExplanation: true,
          includeCitations: true,
          optimizeContext: true,
          useReranking: true
        }
      };

      // Processar através do pipeline avançado
      const advancedResponse = await advancedRAGPipelineService.processQuery(advancedQuery);

      // Registrar métricas
      ragMonitoringService.recordQuery({
        latency: advancedResponse.processingTime,
        confidence: advancedResponse.confidence,
        success: true,
        userId: query.userId,
        query: query.question,
        componentLatencies: {
          retrieval: advancedResponse.pipeline.retrievalTime,
          generation: advancedResponse.pipeline.generationTime,
          total: advancedResponse.processingTime
        }
      });

      // Converter resposta para formato compatível
      const response: RAGResponse = {
        answer: advancedResponse.answer,
        sources: advancedResponse.sources.map(source => ({
          id: source.id,
          content: source.content,
          metadata: source.metadata,
          similarity: source.scores.normalized,
          distance: 1 - source.scores.normalized
        })),
        confidence: advancedResponse.confidence,
        processingTime: advancedResponse.processingTime,
        usedContext: advancedResponse.sources.length > 0,
        metadata: {
          queryType: this.detectQueryType(query.question),
          documentsFound: advancedResponse.metadata.documentsFound,
          averageSimilarity: this.calculateAverageSimilarity(advancedResponse.sources),
          geminiModel: 'gemini-1.5-flash',
          // Novos campos do pipeline avançado
          chunksProcessed: advancedResponse.metadata.chunksProcessed,
          tokensUsed: advancedResponse.metadata.tokensUsed,
          compressionRatio: advancedResponse.metadata.compressionRatio,
          qualityScore: advancedResponse.metadata.qualityScore,
          usedStrategies: advancedResponse.metadata.usedStrategies
        },
        explanation: advancedResponse.explanation,
        citations: advancedResponse.citations,
        pipeline: advancedResponse.pipeline
      };

      console.log(`✅ Consulta processada: ${response.processingTime}ms, confiança: ${(response.confidence * 100).toFixed(1)}%`);
      return response;

    } catch (error: any) {
      console.error('❌ Erro no processamento RAG:', error);

      // Registrar erro nas métricas
      ragMonitoringService.recordQuery({
        latency: 0,
        confidence: 0,
        success: false,
        userId: query.userId,
        query: query.question
      });

      // Retornar resposta de erro compatível
      return {
        answer: 'Desculpe, não foi possível processar sua consulta no momento. Tente novamente.',
        sources: [],
        confidence: 0,
        processingTime: 0,
        usedContext: false,
        metadata: {
          queryType: 'error',
          documentsFound: 0,
          averageSimilarity: 0,
          geminiModel: 'gemini-1.5-flash'
        }
      };
    }
  }

  /**
   * Método auxiliar para construir filtros
   */
  private buildFilters(query: RAGQuery): Record<string, any> {
    const filters: Record<string, any> = {};

    if (query.category) {
      filters.category = query.category;
    }

    if (query.documentType) {
      filters.documentType = query.documentType;
    }

    return filters;
  }

  /**
   * Detectar tipo de query
   */
  private detectQueryType(question: string): string {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('como') || lowerQuestion.includes('procedimento')) {
      return 'procedural';
    } else if (lowerQuestion.includes('quando') || lowerQuestion.includes('prazo')) {
      return 'temporal';
    } else if (lowerQuestion.includes('onde') || lowerQuestion.includes('local')) {
      return 'locational';
    } else if (lowerQuestion.includes('quanto') || lowerQuestion.includes('valor')) {
      return 'financial';
    } else {
      return 'informational';
    }
  }

  /**
   * Calcular similaridade média
   */
  private calculateAverageSimilarity(sources: any[]): number {
    if (sources.length === 0) return 0;

    const totalSimilarity = sources.reduce((sum, source) => {
      return sum + (source.scores?.normalized || source.similarity || 0);
    }, 0);

    return totalSimilarity / sources.length;
  }

  /**
   * Ingerir documentos no sistema
   */
  async ingestDocuments(sources: IngestionSource[]): Promise<{
    successful: number;
    failed: number;
    totalTime: number;
    documentsAdded: number;
    chunksCreated: number;
  }> {
    console.log(`📥 Iniciando ingestão de ${sources.length} documentos...`);

    const result = await advancedRAGPipelineService.ingestDocuments(sources);

    console.log(`✅ Ingestão concluída: ${result.summary.successful}/${sources.length} documentos`);

    return {
      successful: result.summary.successful,
      failed: result.summary.failed,
      totalTime: result.summary.totalProcessingTime,
      documentsAdded: result.summary.documentsAdded,
      chunksCreated: result.summary.chunksCreated
    };
  }

  /**
   * Processar feedback do usuário
   */
  async processFeedback(sessionId: string, feedback: {
    rating: number;
    helpful: boolean;
    comment?: string;
    corrections?: string;
  }): Promise<void> {
    await advancedRAGPipelineService.processFeedback(sessionId, feedback);
    console.log(`📝 Feedback processado para sessão ${sessionId}`);
  }

  /**
   * Obter métricas do sistema
   */
  getMetrics(): any {
    const pipelineMetrics = advancedRAGPipelineService.getMetrics();
    const monitoringMetrics = ragMonitoringService.getMetrics();

    return {
      pipeline: pipelineMetrics,
      monitoring: monitoringMetrics,
      health: ragMonitoringService.generateHealthReport()
    };
  }

  /**
   * Obter configuração atual
   */
  getConfig(): any {
    return advancedRAGPipelineService.getConfig();
  }

  /**
   * Atualizar configuração
   */
  updateConfig(config: any): void {
    advancedRAGPipelineService.updateConfig(config);
    console.log('🔧 Configuração RAG atualizada');
  }

  /**
   * Métodos de compatibilidade com interface antiga
   */

  /**
   * Buscar por categoria específica
   */
  async searchByCategory(
    question: string,
    category: string,
    limit: number = 3
  ): Promise<RAGResponse> {
    return this.processQuery({
      question,
      category,
      maxResults: limit
    });
  }

  /**
   * Buscar informações sobre legislação
   */
  async searchLegislation(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'legislation',
      category: 'legislation',
      maxResults: 3
    });
  }

  /**
   * Buscar informações sobre serviços
   */
  async searchServices(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'service',
      maxResults: 5
    });
  }

  /**
   * Buscar FAQ
   */
  async searchFAQ(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'faq',
      maxResults: 5,
      threshold: 0.4 // Threshold menor para FAQ
    });
  }

  /**
   * Obter estatísticas do serviço RAG
   */
  async getStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<string, number>;
    documentsByCategory: Record<string, number>;
  }> {
    const metrics = this.getMetrics();
    const storage = metrics.monitoring.resources;

    return {
      totalDocuments: storage.totalDocuments || 0,
      documentsByType: {},
      documentsByCategory: {}
    };
  }

  /**
   * Otimizar sistema automaticamente
   */
  async optimizeSystem(): Promise<{
    optimizations: string[];
    expectedImprovement: number;
  }> {
    return advancedRAGPipelineService.optimizePipeline();
  }

  /**
   * Gerar relatório de saúde
   */
  generateHealthReport(): any {
    return ragMonitoringService.generateHealthReport();
  }
}

// Instância singleton
export const ragService = new RAGService();
