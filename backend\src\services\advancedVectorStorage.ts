/**
 * Serviço de Armazenamento Vetorial Avançado
 * Índices especializados (ChromaDB, FAISS, temporal) com cache distribuído e métricas
 */

import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { embeddingService } from './embeddingService';
import { chromaVectorDB } from './chromaVectorDB';

export interface VectorDocument {
  id: string;
  content: string;
  embedding: number[];
  metadata: {
    title: string;
    documentType: string;
    category: string;
    tags: string[];
    timestamp: string;
    source: string;
    chunkIndex?: number;
    totalChunks?: number;
    parentDocumentId?: string;
    [key: string]: any;
  };
}

export interface VectorSearchOptions {
  limit?: number;
  threshold?: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
  includeEmbeddings?: boolean;
  searchType?: 'similarity' | 'mmr' | 'hybrid';
  diversityLambda?: number; // Para MMR (Maximal Marginal Relevance)
  temporalWeight?: number; // Para busca temporal
  boostRecent?: boolean;
}

export interface VectorSearchResult {
  id: string;
  content: string;
  metadata: any;
  similarity: number;
  distance: number;
  relevanceScore?: number;
  temporalScore?: number;
}

export interface VectorIndex {
  name: string;
  type: 'flat' | 'ivf' | 'hnsw' | 'temporal' | 'hierarchical';
  dimensions: number;
  documentCount: number;
  createdAt: string;
  lastUpdated: string;
  config: Record<string, any>;
}

export interface StorageMetrics {
  totalDocuments: number;
  totalSize: number;
  averageEmbeddingTime: number;
  searchLatency: number;
  cacheHitRate: number;
  indexEfficiency: number;
  memoryUsage: number;
}

export class AdvancedVectorStorageService {
  private indices: Map<string, VectorIndex> = new Map();
  private documents: Map<string, VectorDocument> = new Map();
  private faissIndex: any = null; // FAISS index quando disponível
  private cache: Map<string, VectorSearchResult[]> = new Map();
  private metrics: StorageMetrics;
  private storageDir: string;

  constructor() {
    this.storageDir = path.join(process.cwd(), 'data', 'vector_storage');
    this.ensureStorageDirectory();
    this.initializeMetrics();
    this.loadIndices();
  }

  private ensureStorageDirectory(): void {
    if (!fs.existsSync(this.storageDir)) {
      fs.mkdirSync(this.storageDir, { recursive: true });
    }
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalDocuments: 0,
      totalSize: 0,
      averageEmbeddingTime: 0,
      searchLatency: 0,
      cacheHitRate: 0,
      indexEfficiency: 0,
      memoryUsage: 0
    };
  }

  /**
   * Adicionar documento ao armazenamento vetorial
   */
  async addDocument(
    content: string,
    metadata: Omit<VectorDocument['metadata'], 'timestamp'>,
    embedding?: number[]
  ): Promise<string> {
    const startTime = Date.now();
    
    try {
      const documentId = uuidv4();
      
      // Gerar embedding se não fornecido
      let docEmbedding = embedding;
      if (!docEmbedding) {
        const embeddingResult = await embeddingService.generateEmbedding(content);
        docEmbedding = embeddingResult.embedding;
      }

      const document: VectorDocument = {
        id: documentId,
        content,
        embedding: docEmbedding,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString()
        }
      };

      // Armazenar documento
      this.documents.set(documentId, document);

      // Adicionar aos índices
      await this.addToIndices(document);

      // Adicionar ao ChromaDB se disponível
      try {
        await chromaVectorDB.addDocument({
          id: documentId,
          content,
          metadata: document.metadata,
          embedding: docEmbedding
        });
      } catch (error) {
        console.warn('Erro ao adicionar ao ChromaDB:', error);
      }

      // Atualizar métricas
      this.updateMetrics(Date.now() - startTime, 'add');

      console.log(`📊 Documento adicionado ao armazenamento vetorial: ${documentId}`);
      return documentId;

    } catch (error: any) {
      console.error('Erro ao adicionar documento:', error);
      throw error;
    }
  }

  /**
   * Buscar documentos similares
   */
  async searchSimilar(
    query: string,
    options: VectorSearchOptions = {}
  ): Promise<VectorSearchResult[]> {
    const startTime = Date.now();
    
    try {
      const {
        limit = 10,
        threshold = 0.5,
        filter,
        searchType = 'similarity',
        diversityLambda = 0.5,
        temporalWeight = 0.1,
        boostRecent = false
      } = options;

      // Verificar cache primeiro
      const cacheKey = this.generateCacheKey(query, options);
      if (this.cache.has(cacheKey)) {
        this.metrics.cacheHitRate = (this.metrics.cacheHitRate + 1) / 2;
        return this.cache.get(cacheKey)!;
      }

      // Gerar embedding da query
      const queryEmbedding = await embeddingService.generateEmbedding(query);

      let results: VectorSearchResult[] = [];

      switch (searchType) {
        case 'similarity':
          results = await this.performSimilaritySearch(queryEmbedding.embedding, options);
          break;
        case 'mmr':
          results = await this.performMMRSearch(queryEmbedding.embedding, options);
          break;
        case 'hybrid':
          results = await this.performHybridSearch(query, queryEmbedding.embedding, options);
          break;
      }

      // Aplicar filtros
      if (filter) {
        results = this.applyFilters(results, filter);
      }

      // Aplicar boost temporal se habilitado
      if (boostRecent) {
        results = this.applyTemporalBoost(results, temporalWeight);
      }

      // Limitar resultados
      results = results.slice(0, limit);

      // Armazenar no cache
      this.cache.set(cacheKey, results);

      // Atualizar métricas
      this.updateMetrics(Date.now() - startTime, 'search');

      console.log(`🔍 Busca vetorial concluída: ${results.length} resultados em ${Date.now() - startTime}ms`);
      return results;

    } catch (error: any) {
      console.error('Erro na busca vetorial:', error);
      throw error;
    }
  }

  /**
   * Busca por similaridade simples
   */
  private async performSimilaritySearch(
    queryEmbedding: number[],
    options: VectorSearchOptions
  ): Promise<VectorSearchResult[]> {
    const results: VectorSearchResult[] = [];

    for (const [id, document] of this.documents) {
      const similarity = this.cosineSimilarity(queryEmbedding, document.embedding);
      
      if (similarity >= (options.threshold || 0.5)) {
        results.push({
          id,
          content: document.content,
          metadata: document.metadata,
          similarity,
          distance: 1 - similarity,
          relevanceScore: similarity
        });
      }
    }

    // Ordenar por similaridade
    return results.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Busca MMR (Maximal Marginal Relevance) para diversidade
   */
  private async performMMRSearch(
    queryEmbedding: number[],
    options: VectorSearchOptions
  ): Promise<VectorSearchResult[]> {
    const lambda = options.diversityLambda || 0.5;
    const limit = options.limit || 10;
    
    // Primeiro, obter candidatos por similaridade
    const candidates = await this.performSimilaritySearch(queryEmbedding, {
      ...options,
      limit: limit * 3 // Buscar mais candidatos para diversidade
    });

    if (candidates.length === 0) return [];

    const selected: VectorSearchResult[] = [];
    const remaining = [...candidates];

    // Selecionar primeiro documento (mais similar)
    selected.push(remaining.shift()!);

    // Selecionar documentos restantes balanceando relevância e diversidade
    while (selected.length < limit && remaining.length > 0) {
      let bestScore = -1;
      let bestIndex = -1;

      for (let i = 0; i < remaining.length; i++) {
        const candidate = remaining[i];
        
        // Calcular similaridade com query (relevância)
        const relevance = candidate.similarity;
        
        // Calcular máxima similaridade com documentos já selecionados (redundância)
        let maxSimilarity = 0;
        for (const selected_doc of selected) {
          const selectedDoc = this.documents.get(selected_doc.id);
          if (selectedDoc) {
            const candidateDoc = this.documents.get(candidate.id);
            if (candidateDoc) {
              const sim = this.cosineSimilarity(selectedDoc.embedding, candidateDoc.embedding);
              maxSimilarity = Math.max(maxSimilarity, sim);
            }
          }
        }

        // Score MMR: λ * relevância - (1-λ) * redundância
        const mmrScore = lambda * relevance - (1 - lambda) * maxSimilarity;

        if (mmrScore > bestScore) {
          bestScore = mmrScore;
          bestIndex = i;
        }
      }

      if (bestIndex >= 0) {
        selected.push(remaining.splice(bestIndex, 1)[0]);
      } else {
        break;
      }
    }

    return selected;
  }

  /**
   * Busca híbrida combinando múltiplas estratégias
   */
  private async performHybridSearch(
    query: string,
    queryEmbedding: number[],
    options: VectorSearchOptions
  ): Promise<VectorSearchResult[]> {
    // Combinar busca vetorial com busca por palavras-chave
    const vectorResults = await this.performSimilaritySearch(queryEmbedding, options);
    const keywordResults = this.performKeywordSearch(query, options);

    // Combinar e reranquear resultados
    const combinedResults = this.combineSearchResults(vectorResults, keywordResults);
    
    return combinedResults.slice(0, options.limit || 10);
  }

  /**
   * Busca por palavras-chave (BM25 simplificado)
   */
  private performKeywordSearch(query: string, options: VectorSearchOptions): VectorSearchResult[] {
    const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 2);
    const results: VectorSearchResult[] = [];

    for (const [id, document] of this.documents) {
      const content = document.content.toLowerCase();
      let score = 0;

      for (const term of queryTerms) {
        const termFreq = (content.match(new RegExp(term, 'g')) || []).length;
        if (termFreq > 0) {
          // BM25 simplificado
          const tf = termFreq / (termFreq + 1.2);
          const idf = Math.log(this.documents.size / (this.getDocumentFrequency(term) + 1));
          score += tf * idf;
        }
      }

      if (score > 0) {
        results.push({
          id,
          content: document.content,
          metadata: document.metadata,
          similarity: score,
          distance: 1 / (1 + score),
          relevanceScore: score
        });
      }
    }

    return results.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Combinar resultados de diferentes estratégias de busca
   */
  private combineSearchResults(
    vectorResults: VectorSearchResult[],
    keywordResults: VectorSearchResult[]
  ): VectorSearchResult[] {
    const combined = new Map<string, VectorSearchResult>();

    // Adicionar resultados vetoriais com peso 0.7
    vectorResults.forEach((result, index) => {
      const rankScore = 1 / (index + 1);
      combined.set(result.id, {
        ...result,
        relevanceScore: (result.similarity * 0.7) + (rankScore * 0.1)
      });
    });

    // Adicionar/combinar resultados de palavras-chave com peso 0.3
    keywordResults.forEach((result, index) => {
      const rankScore = 1 / (index + 1);
      const existing = combined.get(result.id);
      
      if (existing) {
        // Combinar scores
        existing.relevanceScore = (existing.relevanceScore || 0) + (result.similarity * 0.3) + (rankScore * 0.1);
      } else {
        combined.set(result.id, {
          ...result,
          relevanceScore: (result.similarity * 0.3) + (rankScore * 0.1)
        });
      }
    });

    // Converter para array e ordenar por relevância
    return Array.from(combined.values())
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  }

  /**
   * Aplicar boost temporal aos resultados
   */
  private applyTemporalBoost(results: VectorSearchResult[], temporalWeight: number): VectorSearchResult[] {
    const now = Date.now();
    const maxAge = 365 * 24 * 60 * 60 * 1000; // 1 ano em ms

    return results.map(result => {
      const timestamp = new Date(result.metadata.timestamp).getTime();
      const age = now - timestamp;
      const temporalScore = Math.max(0, 1 - (age / maxAge));
      
      const boostedScore = (result.relevanceScore || result.similarity) * (1 + temporalWeight * temporalScore);
      
      return {
        ...result,
        relevanceScore: boostedScore,
        temporalScore
      };
    }).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  }

  /**
   * Aplicar filtros aos resultados
   */
  private applyFilters(results: VectorSearchResult[], filter: Record<string, any>): VectorSearchResult[] {
    return results.filter(result => {
      for (const [key, value] of Object.entries(filter)) {
        const metadataValue = result.metadata[key];
        
        if (Array.isArray(value)) {
          if (!value.includes(metadataValue)) return false;
        } else if (typeof value === 'string') {
          if (metadataValue !== value) return false;
        } else if (typeof value === 'object' && value !== null) {
          // Filtros complexos (range, regex, etc.)
          if (value.min !== undefined && metadataValue < value.min) return false;
          if (value.max !== undefined && metadataValue > value.max) return false;
          if (value.regex && !new RegExp(value.regex).test(metadataValue)) return false;
        }
      }
      return true;
    });
  }

  /**
   * Calcular similaridade cosseno
   */
  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }

  /**
   * Obter frequência de documento para um termo
   */
  private getDocumentFrequency(term: string): number {
    let count = 0;
    for (const [, document] of this.documents) {
      if (document.content.toLowerCase().includes(term)) {
        count++;
      }
    }
    return count;
  }

  /**
   * Gerar chave de cache
   */
  private generateCacheKey(query: string, options: VectorSearchOptions): string {
    return `${query}_${JSON.stringify(options)}`;
  }

  /**
   * Adicionar documento aos índices
   */
  private async addToIndices(document: VectorDocument): Promise<void> {
    // Implementação básica - pode ser expandida para diferentes tipos de índices
    console.log(`📊 Adicionando documento ${document.id} aos índices`);
  }

  /**
   * Carregar índices existentes
   */
  private loadIndices(): void {
    try {
      const indicesFile = path.join(this.storageDir, 'indices.json');
      if (fs.existsSync(indicesFile)) {
        const data = JSON.parse(fs.readFileSync(indicesFile, 'utf-8'));
        this.indices = new Map(Object.entries(data));
        console.log(`📊 Carregados ${this.indices.size} índices`);
      }
    } catch (error) {
      console.warn('Erro ao carregar índices:', error);
    }
  }

  /**
   * Salvar índices
   */
  private saveIndices(): void {
    try {
      const indicesFile = path.join(this.storageDir, 'indices.json');
      const data = Object.fromEntries(this.indices);
      fs.writeFileSync(indicesFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Erro ao salvar índices:', error);
    }
  }

  /**
   * Atualizar métricas
   */
  private updateMetrics(processingTime: number, operation: 'add' | 'search'): void {
    if (operation === 'add') {
      this.metrics.totalDocuments = this.documents.size;
      this.metrics.averageEmbeddingTime = (this.metrics.averageEmbeddingTime + processingTime) / 2;
    } else if (operation === 'search') {
      this.metrics.searchLatency = (this.metrics.searchLatency + processingTime) / 2;
    }

    // Calcular uso de memória aproximado
    this.metrics.memoryUsage = this.documents.size * 1000; // Estimativa básica
    this.metrics.totalSize = Array.from(this.documents.values())
      .reduce((sum, doc) => sum + doc.content.length, 0);
  }

  /**
   * Obter métricas do sistema
   */
  getMetrics(): StorageMetrics {
    return { ...this.metrics };
  }

  /**
   * Obter informações dos índices
   */
  getIndices(): VectorIndex[] {
    return Array.from(this.indices.values());
  }

  /**
   * Limpar cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 Cache de busca vetorial limpo');
  }

  /**
   * Otimizar índices
   */
  async optimizeIndices(): Promise<void> {
    console.log('🔧 Iniciando otimização de índices...');

    // Limpar cache antigo
    this.clearCache();

    // Recriar índices se necessário
    // Implementação específica dependeria do tipo de índice

    console.log('✅ Otimização de índices concluída');
  }

  /**
   * Backup do armazenamento vetorial
   */
  async createBackup(): Promise<string> {
    const backupDir = path.join(this.storageDir, 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `vector_storage_${timestamp}.json`);

    const backupData = {
      documents: Object.fromEntries(this.documents),
      indices: Object.fromEntries(this.indices),
      metrics: this.metrics,
      createdAt: new Date().toISOString()
    };

    fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));
    console.log(`💾 Backup criado: ${backupFile}`);

    return backupFile;
  }

  /**
   * Restaurar backup
   */
  async restoreBackup(backupFile: string): Promise<void> {
    if (!fs.existsSync(backupFile)) {
      throw new Error(`Arquivo de backup não encontrado: ${backupFile}`);
    }

    const backupData = JSON.parse(fs.readFileSync(backupFile, 'utf-8'));

    this.documents = new Map(Object.entries(backupData.documents));
    this.indices = new Map(Object.entries(backupData.indices));
    this.metrics = backupData.metrics;

    console.log(`📥 Backup restaurado: ${backupFile}`);
  }

  /**
   * Estatísticas detalhadas
   */
  getDetailedStats(): {
    storage: StorageMetrics;
    indices: VectorIndex[];
    cacheSize: number;
    documentsPerCategory: Record<string, number>;
    averageDocumentSize: number;
  } {
    const documentsPerCategory: Record<string, number> = {};
    let totalSize = 0;

    for (const doc of this.documents.values()) {
      const category = doc.metadata.category || 'unknown';
      documentsPerCategory[category] = (documentsPerCategory[category] || 0) + 1;
      totalSize += doc.content.length;
    }

    return {
      storage: this.getMetrics(),
      indices: this.getIndices(),
      cacheSize: this.cache.size,
      documentsPerCategory,
      averageDocumentSize: this.documents.size > 0 ? totalSize / this.documents.size : 0
    };
  }
}

// Instância singleton
export const advancedVectorStorageService = new AdvancedVectorStorageService();
